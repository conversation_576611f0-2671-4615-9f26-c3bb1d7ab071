#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
سكريبت تثبيت نظام أرشفة الكتب الإلكترونية
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def install_requirements():
    """تثبيت المتطلبات"""
    print("📦 تثبيت المتطلبات...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ تم تثبيت المتطلبات بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت المتطلبات: {e}")
        return False

def create_desktop_shortcut():
    """إنشاء اختصار على سطح المكتب"""
    if sys.platform == "win32":
        try:
            import winshell
            from win32com.client import Dispatch
            
            desktop = winshell.desktop()
            path = os.path.join(desktop, "نظام أرشفة الكتب.lnk")
            target = os.path.join(os.getcwd(), "main.py")
            
            shell = Dispatch('WScript.Shell')
            shortcut = shell.CreateShortCut(path)
            shortcut.Targetpath = sys.executable
            shortcut.Arguments = f'"{target}"'
            shortcut.WorkingDirectory = os.getcwd()
            shortcut.IconLocation = os.path.join(os.getcwd(), "icon.ico")
            shortcut.save()
            
            print("✅ تم إنشاء اختصار على سطح المكتب")
        except ImportError:
            print("⚠️ لا يمكن إنشاء اختصار على سطح المكتب")

def main():
    """الدالة الرئيسية للتثبيت"""
    print("🚀 بدء تثبيت نظام أرشفة الكتب الإلكترونية")
    print("=" * 50)
    
    # تثبيت المتطلبات
    if not install_requirements():
        print("❌ فشل التثبيت")
        return False
    
    # إنشاء اختصار
    create_desktop_shortcut()
    
    print("=" * 50)
    print("🎉 تم التثبيت بنجاح!")
    print("يمكنك الآن تشغيل البرنامج من خلال:")
    print(f"python {os.path.join(os.getcwd(), 'main.py')}")
    
    return True

if __name__ == "__main__":
    main()
