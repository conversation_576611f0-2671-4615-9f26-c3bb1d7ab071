#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
برنامج أرشفة المستندات الورقية
تطبيق سطح مكتب لإدارة وأرشفة المستندات الورقية
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QPushButton, QLabel, QLineEdit,
                            QFrame, QStackedWidget, QSizePolicy, QMessageBox, QGraphicsDropShadowEffect)
from PyQt5.QtGui import QIcon, QFont, QPixmap, QColor
from PyQt5.QtCore import Qt, QSize, QPoint

# استيراد المكونات الأخرى
from database import Database
from login import LoginWindow
from departments import DepartmentsWidget
from books_view import BooksWidget
from book_manager import BookManager
from users import UsersWidget
from settings import SettingsWidget
from updater import UpdateDialog, get_current_version

# تعريف الألوان المستخدمة في الواجهة
COLORS = {
    "dark_blue": "#1a3a63",
    "light_blue": "#1e81b0",
    "green": "#16a085",
    "pink": "#e91e63",
    "white": "#ffffff",
    "light_gray": "#f5f5f5",
    "dark_gray": "#333333",
}

class MainWindow(QMainWindow):
    """النافذة الرئيسية للتطبيق"""

    def __init__(self):
        super().__init__()

        # تهيئة قاعدة البيانات
        self.db = Database()

        # المستخدم الحالي
        self.current_user = None

        # إعداد النافذة الرئيسية
        self.setWindowTitle("نظام أرشفة الكتب الإلكترونية للنسخة المجانية 2024")
        self.setMinimumSize(1200, 700)
        self.setStyleSheet(f"""
            QMainWindow {{
                background-color: {COLORS['light_gray']};
                font-family: 'Arial';
            }}
            QLabel {{
                font-family: 'Arial';
            }}
            QPushButton {{
                font-family: 'Arial';
            }}
            QLineEdit {{
                font-family: 'Arial';
            }}
            QComboBox {{
                font-family: 'Arial';
            }}
            QTableWidget {{
                font-family: 'Arial';
            }}
            QTreeWidget {{
                font-family: 'Arial';
            }}
        """)

        # إنشاء الواجهة الرئيسية
        self.init_ui()

        # تعطيل الوصول إلى الصفحات التي تتطلب تسجيل الدخول
        self.disable_authenticated_pages()

        # عرض النافذة الرئيسية أولاً
        self.show()

        # ثم عرض نافذة تسجيل الدخول
        self.show_login_window()

    def closeEvent(self, a0):
        """تنفيذ عند إغلاق النافذة"""
        # إغلاق الاتصال بقاعدة البيانات
        if hasattr(self, 'db') and self.db:
            self.db.close()
        # استدعاء الدالة الأصلية
        super().closeEvent(a0)

    def init_ui(self):
        """تهيئة واجهة المستخدم"""

        # تعيين اتجاه التطبيق من اليمين إلى اليسار
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # الحاوية الرئيسية
        central_widget = QWidget()
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # تعيين اتجاه التخطيط من اليمين إلى اليسار
        # لا نحتاج لتعيين اتجاه التخطيط لأننا سنضع القائمة الجانبية أولاً

        # إنشاء المحتوى الرئيسي
        content_area = QWidget()
        content_area.setObjectName("contentArea")
        content_area.setStyleSheet("""
            #contentArea {
                background-color: #f5f5f5;
                border-radius: 0px;
            }
        """)

        content_layout = QVBoxLayout(content_area)
        content_layout.setContentsMargins(10, 10, 10, 10)
        content_layout.setSpacing(10)

        # إضافة شريط الإحصائيات العلوي
        self.stats_bar = self.create_stats_bar()
        content_layout.addWidget(self.stats_bar)

        # إنشاء منطقة المحتوى الرئيسية
        self.stacked_widget = QStackedWidget()

        # إنشاء مدير الكتب
        self.book_manager = BookManager(self.db)

        # إنشاء الصفحات المختلفة
        self.home_page = self.create_home_page()
        self.departments_page = DepartmentsWidget(self.db)
        self.books_page = BooksWidget(self.db)
        self.users_page = UsersWidget(self.db)
        self.settings_page = SettingsWidget(self.db)

        # إضافة الصفحات إلى الـ stacked widget
        self.stacked_widget.addWidget(self.home_page)  # index 0
        self.stacked_widget.addWidget(self.departments_page)  # index 1
        self.stacked_widget.addWidget(self.books_page)  # index 2
        self.stacked_widget.addWidget(self.users_page)  # index 3
        self.stacked_widget.addWidget(self.settings_page)  # index 4

        content_layout.addWidget(self.stacked_widget)

        # إنشاء القائمة الجانبية
        sidebar = self.create_sidebar()

        # إضافة المكونات إلى التخطيط الرئيسي
        main_layout.addWidget(sidebar, 1)  # نسبة أصغر للقائمة الجانبية (على اليمين)
        main_layout.addWidget(content_area, 4)  # نسبة أكبر للمحتوى

        self.setCentralWidget(central_widget)

        # تحديث الإحصائيات
        self.update_stats()

    def create_sidebar(self):
        """إنشاء القائمة الجانبية"""
        sidebar = QWidget()
        sidebar.setStyleSheet(f"""
            background-color: {COLORS['dark_blue']};
            padding: 0;
            margin: 0;
        """)

        sidebar_layout = QVBoxLayout(sidebar)
        sidebar_layout.setContentsMargins(0, 0, 0, 0)
        sidebar_layout.setSpacing(0)
        # تعيين محاذاة العناصر إلى الأعلى
        sidebar_layout.setAlignment(Qt.AlignmentFlag.AlignTop)

        # عنوان القائمة الجانبية
        title_label = QLabel("نظام الأرشفة الإلكترونية")
        title_label.setStyleSheet(f"""
            color: {COLORS['white']};
            font-size: 16px;
            font-weight: bold;
            padding: 20px 10px;
            text-align: right;
        """)
        title_label.setAlignment(Qt.AlignmentFlag.AlignRight)

        # أزرار القائمة
        menu_items = [
            {"text": "الرئيسية", "icon": "home", "page": 0},
            {"text": "الأقسام", "icon": "departments", "page": 1},
            {"text": "الكتب", "icon": "books", "page": 2},
            {"text": "المستخدمون", "icon": "users", "page": 3},
            {"text": "الإعدادات", "icon": "settings", "page": 4},
        ]

        self.menu_buttons = []
        for item in menu_items:
            btn = QPushButton(item["text"])
            btn.setStyleSheet(f"""
                QPushButton {{
                    color: {COLORS['white']};
                    background-color: transparent;
                    border: none;
                    text-align: right;
                    padding: 15px 20px;
                    font-size: 14px;
                    margin-right: 0;
                    margin-left: auto;
                }}
                QPushButton:hover {{
                    background-color: rgba(255, 255, 255, 0.1);
                }}
            """)
            # تعيين محاذاة النص إلى اليمين
            btn.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
            btn.setContentsMargins(0, 0, 10, 0)

            # ربط الزر بالصفحة المناسبة
            page_index = item["page"]
            btn.clicked.connect(lambda _, idx=page_index: self.stacked_widget.setCurrentIndex(idx))

            self.menu_buttons.append(btn)
            sidebar_layout.addWidget(btn)

        # معلومات المستخدم
        self.user_info_label = QLabel("مرحبًا، زائر")
        self.user_info_label.setStyleSheet(f"""
            color: {COLORS['white']};
            font-size: 14px;
            padding: 10px 20px;
            text-align: right;
        """)
        self.user_info_label.setAlignment(Qt.AlignmentFlag.AlignRight)

        # زر تسجيل الخروج
        self.logout_btn = QPushButton("تسجيل الخروج")
        self.logout_btn.setStyleSheet(f"""
            QPushButton {{
                color: {COLORS['white']};
                background-color: {COLORS['pink']};
                border: none;
                border-radius: 4px;
                padding: 10px;
                margin: 20px;
                font-size: 14px;
                text-align: center;
            }}
            QPushButton:hover {{
                background-color: #d81b60;
            }}
        """)
        self.logout_btn.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        self.logout_btn.clicked.connect(self.logout)

        # إضافة مساحة فارغة قبل زر تسجيل الخروج
        spacer = QWidget()
        spacer.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # زر فحص التحديثات
        self.update_btn = QPushButton("فحص التحديثات")
        self.update_btn.setStyleSheet(f"""
            QPushButton {{
                color: {COLORS['white']};
                background-color: {COLORS['green']};
                border: none;
                border-radius: 4px;
                padding: 8px;
                margin: 10px 20px;
                font-size: 12px;
                text-align: center;
            }}
            QPushButton:hover {{
                background-color: #138d75;
            }}
        """)
        self.update_btn.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        self.update_btn.clicked.connect(self.check_for_updates)

        # إضافة معلومات الإصدار
        version_label = QLabel(f"إصدار البرنامج: {get_current_version()}")
        version_label.setStyleSheet(f"""
            color: {COLORS['white']};
            padding: 10px;
            font-size: 12px;
            text-align: right;
        """)
        version_label.setAlignment(Qt.AlignmentFlag.AlignRight)

        sidebar_layout.addWidget(title_label)
        sidebar_layout.addStretch(1)
        sidebar_layout.addWidget(self.user_info_label)
        sidebar_layout.addWidget(self.update_btn)
        sidebar_layout.addWidget(self.logout_btn)
        sidebar_layout.addWidget(version_label)

        return sidebar

    def create_stats_bar(self):
        """إنشاء شريط الإحصائيات العلوي"""
        stats_bar = QWidget()
        stats_layout = QHBoxLayout(stats_bar)
        stats_layout.setContentsMargins(10, 10, 10, 10)
        stats_layout.setSpacing(10)

        # إنشاء البطاقات الإحصائية
        self.stats_cards = {
            "departments": self.create_stat_card("الأقسام", "0", COLORS["dark_blue"]),
            "users": self.create_stat_card("المستخدمين", "0", COLORS["green"]),
            "books": self.create_stat_card("الكتب", "0", COLORS["pink"]),
            "size": self.create_stat_card("حجم الملفات", "0.0 MB", COLORS["light_blue"]),
        }

        for card in self.stats_cards.values():
            stats_layout.addWidget(card)

        return stats_bar

    def create_stat_card(self, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setFrameShape(QFrame.StyledPanel)
        card.setStyleSheet(f"""
            QFrame {{
                border: none;
                border-radius: 10px;
                background-color: {COLORS['white']};
                box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.1);
            }}
        """)

        # إضافة تأثير الظل
        shadow = QGraphicsDropShadowEffect(self)
        shadow.setBlurRadius(15)
        shadow.setColor(QColor(0, 0, 0, 30))
        shadow.setOffset(0, 3)
        card.setGraphicsEffect(shadow)

        card_layout = QHBoxLayout(card)

        # مؤشر اللون
        color_indicator = QFrame()
        color_indicator.setStyleSheet(f"background-color: {color};")
        color_indicator.setFixedWidth(10)
        color_indicator.setFrameShape(QFrame.NoFrame)

        # محتوى البطاقة
        content = QWidget()
        content_layout = QHBoxLayout(content)
        content_layout.setContentsMargins(10, 10, 10, 10)

        # القيمة
        value_label = QLabel(value)
        value_label.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
        """)
        card.value_label = value_label  # تخزين مرجع للتحديث لاحقًا

        # العنوان
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            color: {color};
            font-size: 16px;
            font-weight: bold;
        """)

        content_layout.addWidget(value_label)
        content_layout.addWidget(title_label)
        content_layout.setAlignment(Qt.AlignmentFlag.AlignRight)

        card_layout.addWidget(color_indicator)
        card_layout.addWidget(content, 1)

        return card

    def create_home_page(self):
        """إنشاء الصفحة الرئيسية"""
        content = QWidget()
        content.setObjectName("homePage")
        content.setStyleSheet("""
            #homePage {
                background-color: white;
                border-radius: 15px;
            }
        """)

        # إضافة تأثير الظل
        shadow = QGraphicsDropShadowEffect(self)
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 30))
        shadow.setOffset(0, 0)
        content.setGraphicsEffect(shadow)

        content_layout = QVBoxLayout(content)
        content_layout.setContentsMargins(30, 30, 30, 30)
        content_layout.setSpacing(25)

        # عنوان الوصول السريع
        quick_access_label = QLabel("الوصول السريع")
        quick_access_label.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #1a3a63;
            margin-bottom: 10px;
        """)
        quick_access_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # حقل البحث
        search_container = QWidget()
        search_container.setObjectName("searchContainer")
        search_container.setStyleSheet("""
            #searchContainer {
                background-color: #f9f9f9;
                border-radius: 10px;
                padding: 10px;
            }
        """)
        search_layout = QHBoxLayout(search_container)
        search_layout.setContentsMargins(10, 10, 10, 10)
        search_layout.setSpacing(10)

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("بحث في الكتب...")
        self.search_input.setMinimumHeight(45)
        self.search_input.setStyleSheet("""
            QLineEdit {
                padding: 12px 15px;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit:focus {
                border: 2px solid #1a3a63;
            }
        """)

        search_button = QPushButton("بحث")
        search_button.setMinimumHeight(45)
        search_button.setStyleSheet(f"""
            QPushButton {{
                background-color: #1a3a63;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #2c5282;
            }}
            QPushButton:pressed {{
                background-color: #0f2342;
            }}
        """)
        search_button.clicked.connect(self.search_books)

        search_layout.addWidget(search_button)
        search_layout.addWidget(self.search_input)

        # زر إضافة كتاب جديد
        add_book_button = QPushButton("إضافة كتاب جديد")
        add_book_button.setMinimumHeight(60)
        add_book_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLORS['pink']};
                color: white;
                border: none;
                padding: 15px;
                border-radius: 10px;
                font-size: 18px;
                font-weight: bold;
                margin-top: 20px;
            }}
            QPushButton:hover {{
                background-color: #d81b60;
            }}
            QPushButton:pressed {{
                background-color: #ad1457;
            }}
        """)
        add_book_button.clicked.connect(self.add_book)



        # إضافة معلومات النظام
        system_info = QWidget()
        system_info.setObjectName("systemInfo")
        system_info.setStyleSheet("""
            #systemInfo {
                background-color: #1a3a63;
                border-radius: 10px;
                padding: 15px;
                color: white;
            }
        """)

        system_info_layout = QVBoxLayout(system_info)

        system_title = QLabel("معلومات النظام")
        system_title.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: white;
            margin-bottom: 10px;
        """)

        system_info_layout.addWidget(system_title)

        # معلومات النظام
        info_layout = QHBoxLayout()

        for info_name, info_value in [
            ("إجمالي الكتب", "0"),
            ("المستخدمين النشطين", "1"),
            ("آخر تحديث", "اليوم")
        ]:
            info_widget = QWidget()
            info_widget_layout = QVBoxLayout(info_widget)

            info_label = QLabel(info_name)
            info_label.setStyleSheet("color: #ccc; font-size: 12px;")

            info_value_label = QLabel(info_value)
            info_value_label.setStyleSheet("color: white; font-size: 18px; font-weight: bold;")

            info_widget_layout.addWidget(info_label)
            info_widget_layout.addWidget(info_value_label)

            info_layout.addWidget(info_widget)

        system_info_layout.addLayout(info_layout)

        # إضافة العناصر إلى التخطيط
        content_layout.addWidget(quick_access_label)
        content_layout.addWidget(search_container)
        content_layout.addWidget(add_book_button)
        content_layout.addWidget(system_info)
        content_layout.addStretch(1)

        return content

    def show_login_window(self):
        """عرض نافذة تسجيل الدخول"""
        # التحقق مما إذا كان المستخدم قد سجل الدخول مسبقًا
        if self.current_user:
            return

        # إنشاء نافذة تسجيل الدخول كعضو في الفئة
        self.login_window = LoginWindow(on_login_success=self.on_login_success)

        # تعيين النافذة الرئيسية كوالد لنافذة تسجيل الدخول
        self.login_window.setParent(None)  # إزالة الوالد لتجنب المشاكل
        self.login_window.setWindowModality(Qt.WindowModality.ApplicationModal)

        # عرض النافذة بشكل عادي
        self.login_window.show()

        # تعطيل الوصول إلى الصفحات التي تتطلب تسجيل الدخول
        self.disable_authenticated_pages()

    def on_login_success(self, user):
        """تم تسجيل الدخول بنجاح"""
        self.current_user = user
        self.user_info_label.setText(f"مرحبًا، {user['full_name'] or user['username']}")

        # تعيين المستخدم الحالي للصفحات
        self.books_page.set_current_user(user)

        # تمكين الوصول إلى جميع الصفحات
        self.enable_authenticated_pages()

        # تحديث الإحصائيات
        self.update_stats()

    def check_for_updates(self):
        """فحص التحديثات"""
        update_dialog = UpdateDialog(self)
        update_dialog.exec()

    def logout(self):
        """تسجيل الخروج"""
        self.current_user = None
        self.user_info_label.setText("مرحبًا، زائر")

        # العودة إلى الصفحة الرئيسية
        self.stacked_widget.setCurrentIndex(0)

        # تعطيل الوصول إلى الصفحات التي تتطلب تسجيل الدخول
        self.disable_authenticated_pages()

        # عرض نافذة تسجيل الدخول مرة أخرى
        self.show_login_window()

    def disable_authenticated_pages(self):
        """تعطيل الوصول إلى الصفحات التي تتطلب تسجيل الدخول"""
        # بدلاً من تعطيل الأزرار، سنقوم بتعديل سلوكها عند النقر عليها
        for i, btn in enumerate(self.menu_buttons):
            # الصفحة الرئيسية متاحة للجميع
            if i == 0:
                continue

            # إعادة ربط الزر بدالة تعرض رسالة تسجيل الدخول
            try:
                btn.clicked.disconnect()  # فصل الاتصال السابق
            except TypeError:
                pass  # لا يوجد اتصال سابق
            btn.clicked.connect(lambda _: self.show_login_required_message())

            # تغيير لون الزر ليظهر أنه غير متاح
            btn.setStyleSheet(f"""
                QPushButton {{
                    color: #999999;
                    background-color: transparent;
                    border: none;
                    text-align: right;
                    padding: 15px 20px;
                    font-size: 14px;
                    margin-right: 0;
                    margin-left: auto;
                }}
                QPushButton:hover {{
                    background-color: rgba(255, 255, 255, 0.05);
                }}
            """)
            # تعيين محاذاة النص إلى اليمين
            btn.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
            btn.setContentsMargins(0, 0, 10, 0)

    def enable_authenticated_pages(self):
        """تمكين الوصول إلى جميع الصفحات"""
        # تمكين جميع أزرار القائمة
        for i, btn in enumerate(self.menu_buttons):
            # إعادة ربط الزر بالصفحة المناسبة
            page_index = i
            try:
                btn.clicked.disconnect()  # فصل الاتصال السابق
            except TypeError:
                pass  # لا يوجد اتصال سابق
            btn.clicked.connect(lambda _, idx=page_index: self.stacked_widget.setCurrentIndex(idx))

            btn.setStyleSheet(f"""
                QPushButton {{
                    color: {COLORS['white']};
                    background-color: transparent;
                    border: none;
                    text-align: right;
                    padding: 15px 20px;
                    font-size: 14px;
                    margin-right: 0;
                    margin-left: auto;
                }}
                QPushButton:hover {{
                    background-color: rgba(255, 255, 255, 0.1);
                }}
            """)
            # تعيين محاذاة النص إلى اليمين
            btn.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
            btn.setContentsMargins(0, 0, 10, 0)

    def show_login_required_message(self):
        """عرض رسالة تطلب من المستخدم تسجيل الدخول"""
        QMessageBox.information(
            self,
            "تسجيل الدخول مطلوب",
            "يجب تسجيل الدخول للوصول إلى هذه الصفحة",
            QMessageBox.StandardButton.Ok
        )

        # عرض نافذة تسجيل الدخول
        self.show_login_window()

    def update_stats(self):
        """تحديث الإحصائيات"""
        stats = self.db.get_stats()

        # تحديث بطاقات الإحصائيات
        self.stats_cards["departments"].value_label.setText(str(stats['departments_count']))
        self.stats_cards["users"].value_label.setText(str(stats['users_count']))
        self.stats_cards["books"].value_label.setText(str(stats['books_count']))

        # تنسيق حجم الملفات
        total_size = stats['total_size']
        if total_size < 1024:
            size_str = f"{total_size} بايت"
        elif total_size < 1024 * 1024:
            size_str = f"{total_size / 1024:.2f} كيلوبايت"
        else:
            size_str = f"{total_size / (1024 * 1024):.2f} ميجابايت"

        self.stats_cards["size"].value_label.setText(size_str)

    def search_books(self):
        """البحث في الكتب"""
        search_text = self.search_input.text()
        if search_text:
            # الانتقال إلى صفحة الكتب
            self.stacked_widget.setCurrentIndex(2)

            # تنفيذ البحث
            self.books_page.search_input.setText(search_text)
            self.books_page.search_books()

    def add_book(self):
        """إضافة كتاب جديد"""
        if not self.current_user:
            QMessageBox.warning(self, "خطأ", "يجب تسجيل الدخول أولاً")
            return

        # الانتقال إلى صفحة الكتب
        self.stacked_widget.setCurrentIndex(2)

        # فتح نافذة إضافة كتاب
        self.books_page.add_book()


def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    app = QApplication(sys.argv)

    # تعيين اتجاه التطبيق من اليمين إلى اليسار للغة العربية
    app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

    # تعيين الخط للتطبيق
    font = QFont("Arial", 10)
    app.setFont(font)

    window = MainWindow()
    # النافذة ستظهر تلقائياً من خلال __init__

    sys.exit(app.exec())


if __name__ == "__main__":
    main()
