#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
برنامج أرشفة المستندات الورقية
تطبيق سطح مكتب لإدارة وأرشفة المستندات الورقية
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QPushButton, QLabel, QLineEdit,
                            QFrame, QStackedWidget, QSizePolicy, QMessageBox)
from PyQt5.QtGui import QIcon, QFont, QPixmap, QColor
from PyQt5.QtCore import Qt, QSize

# استيراد المكونات الأخرى
from database import Database
from login import LoginWindow
from departments import DepartmentsWidget
from documents_view import DocumentsWidget
from document_manager import DocumentManager
from users import UsersWidget
from settings import SettingsWidget
from archive import ArchiveWidget

# تعريف الألوان المستخدمة في الواجهة
COLORS = {
    "dark_blue": "#1a3a63",
    "light_blue": "#1e81b0",
    "green": "#16a085",
    "pink": "#e91e63",
    "white": "#ffffff",
    "light_gray": "#f5f5f5",
    "dark_gray": "#333333",
}

class MainWindow(QMainWindow):
    """النافذة الرئيسية للتطبيق"""

    def __init__(self):
        super().__init__()

        # تهيئة قاعدة البيانات
        self.db = Database()

        # المستخدم الحالي
        self.current_user = None

        # إعداد النافذة الرئيسية
        self.setWindowTitle("نظام الأرشفة الإلكترونية للنسخة المجانية 2022")
        self.setMinimumSize(1000, 600)
        self.setStyleSheet(f"background-color: {COLORS['light_gray']};")

        # إنشاء الواجهة الرئيسية
        self.init_ui()

        # تعطيل الوصول إلى الصفحات التي تتطلب تسجيل الدخول
        self.disable_authenticated_pages()

        # عرض نافذة تسجيل الدخول
        self.show_login_window()

    def closeEvent(self, a0):
        """تنفيذ عند إغلاق النافذة"""
        # إغلاق الاتصال بقاعدة البيانات
        if hasattr(self, 'db') and self.db:
            self.db.close()
        # استدعاء الدالة الأصلية
        super().closeEvent(a0)

    def init_ui(self):
        """تهيئة واجهة المستخدم"""

        # تعيين اتجاه التطبيق من اليمين إلى اليسار
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # الحاوية الرئيسية
        central_widget = QWidget()
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # إنشاء المحتوى الرئيسي
        content_area = QWidget()
        content_layout = QVBoxLayout(content_area)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)

        # إضافة شريط الإحصائيات العلوي
        self.stats_bar = self.create_stats_bar()
        content_layout.addWidget(self.stats_bar)

        # إنشاء منطقة المحتوى الرئيسية
        self.stacked_widget = QStackedWidget()

        # إنشاء مدير المستندات
        self.document_manager = DocumentManager(self.db)

        # إنشاء الصفحات المختلفة
        self.home_page = self.create_home_page()
        self.departments_page = DepartmentsWidget(self.db)
        self.documents_page = DocumentsWidget(self.db)
        self.archive_page = ArchiveWidget(self.db, self.document_manager)
        self.users_page = UsersWidget(self.db)
        self.settings_page = SettingsWidget(self.db)

        # إضافة الصفحات إلى الـ stacked widget
        self.stacked_widget.addWidget(self.home_page)  # index 0
        self.stacked_widget.addWidget(self.departments_page)  # index 1
        self.stacked_widget.addWidget(self.documents_page)  # index 2
        self.stacked_widget.addWidget(self.archive_page)  # index 3
        self.stacked_widget.addWidget(self.users_page)  # index 4
        self.stacked_widget.addWidget(self.settings_page)  # index 5

        content_layout.addWidget(self.stacked_widget)

        # إنشاء القائمة الجانبية
        sidebar = self.create_sidebar()

        # إضافة المكونات إلى التخطيط الرئيسي
        main_layout.addWidget(content_area, 4)  # نسبة أكبر للمحتوى
        main_layout.addWidget(sidebar, 1)  # نسبة أصغر للقائمة الجانبية

        self.setCentralWidget(central_widget)

        # تحديث الإحصائيات
        self.update_stats()

    def create_sidebar(self):
        """إنشاء القائمة الجانبية"""
        sidebar = QWidget()
        sidebar.setStyleSheet(f"background-color: {COLORS['dark_blue']};")

        sidebar_layout = QVBoxLayout(sidebar)
        sidebar_layout.setContentsMargins(0, 0, 0, 0)
        sidebar_layout.setSpacing(0)
        sidebar_layout.setAlignment(Qt.AlignmentFlag.AlignTop)

        # عنوان القائمة الجانبية
        title_label = QLabel("نظام الأرشفة الإلكترونية")
        title_label.setStyleSheet(f"""
            color: {COLORS['white']};
            font-size: 16px;
            font-weight: bold;
            padding: 20px 10px;
            text-align: center;
        """)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # أزرار القائمة
        menu_items = [
            {"text": "الرئيسية", "icon": "home", "page": 0},
            {"text": "الأقسام", "icon": "departments", "page": 1},
            {"text": "المستندات", "icon": "documents", "page": 2},
            {"text": "الأرشيف", "icon": "archive", "page": 3},
            {"text": "المستخدمون", "icon": "users", "page": 4},
            {"text": "الإعدادات", "icon": "settings", "page": 5},
        ]

        self.menu_buttons = []
        for item in menu_items:
            btn = QPushButton(item["text"])
            btn.setStyleSheet(f"""
                QPushButton {{
                    color: {COLORS['white']};
                    background-color: transparent;
                    border: none;
                    text-align: right;
                    padding: 15px 20px;
                    font-size: 14px;
                }}
                QPushButton:hover {{
                    background-color: rgba(255, 255, 255, 0.1);
                }}
            """)
            btn.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

            # ربط الزر بالصفحة المناسبة
            page_index = item["page"]
            btn.clicked.connect(lambda _, idx=page_index: self.stacked_widget.setCurrentIndex(idx))

            self.menu_buttons.append(btn)
            sidebar_layout.addWidget(btn)

        # معلومات المستخدم
        self.user_info_label = QLabel("مرحبًا، زائر")
        self.user_info_label.setStyleSheet(f"""
            color: {COLORS['white']};
            font-size: 14px;
            padding: 10px 20px;
        """)

        # زر تسجيل الخروج
        self.logout_btn = QPushButton("تسجيل الخروج")
        self.logout_btn.setStyleSheet(f"""
            QPushButton {{
                color: {COLORS['white']};
                background-color: {COLORS['pink']};
                border: none;
                border-radius: 4px;
                padding: 10px;
                margin: 20px;
                font-size: 14px;
            }}
            QPushButton:hover {{
                background-color: #d81b60;
            }}
        """)
        self.logout_btn.clicked.connect(self.logout)

        # إضافة مساحة فارغة قبل زر تسجيل الخروج
        spacer = QWidget()
        spacer.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # إضافة معلومات الإصدار
        version_label = QLabel("إصدار البرنامج: 1.0")
        version_label.setStyleSheet(f"color: {COLORS['white']}; padding: 10px; font-size: 12px;")
        version_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        sidebar_layout.addWidget(title_label)
        sidebar_layout.addStretch(1)
        sidebar_layout.addWidget(self.user_info_label)
        sidebar_layout.addWidget(self.logout_btn)
        sidebar_layout.addWidget(version_label)

        return sidebar

    def create_stats_bar(self):
        """إنشاء شريط الإحصائيات العلوي"""
        stats_bar = QWidget()
        stats_layout = QHBoxLayout(stats_bar)
        stats_layout.setContentsMargins(10, 10, 10, 10)
        stats_layout.setSpacing(10)

        # إنشاء البطاقات الإحصائية
        self.stats_cards = {
            "departments": self.create_stat_card("الأقسام", "0", COLORS["dark_blue"]),
            "users": self.create_stat_card("المستخدمين", "0", COLORS["green"]),
            "documents": self.create_stat_card("المستندات", "0", COLORS["pink"]),
            "size": self.create_stat_card("حجم الملفات", "0.0 MB", COLORS["pink"]),
        }

        for card in self.stats_cards.values():
            stats_layout.addWidget(card)

        return stats_bar

    def create_stat_card(self, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setFrameShape(QFrame.StyledPanel)
        card.setStyleSheet(f"""
            QFrame {{
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: {COLORS['white']};
            }}
        """)

        card_layout = QHBoxLayout(card)

        # مؤشر اللون
        color_indicator = QFrame()
        color_indicator.setStyleSheet(f"background-color: {color};")
        color_indicator.setFixedWidth(10)
        color_indicator.setFrameShape(QFrame.NoFrame)

        # محتوى البطاقة
        content = QWidget()
        content_layout = QHBoxLayout(content)
        content_layout.setContentsMargins(10, 10, 10, 10)

        # القيمة
        value_label = QLabel(value)
        value_label.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
        """)
        card.value_label = value_label  # تخزين مرجع للتحديث لاحقًا

        # العنوان
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            color: {color};
            font-size: 16px;
            font-weight: bold;
        """)

        content_layout.addWidget(value_label)
        content_layout.addWidget(title_label)
        content_layout.setAlignment(Qt.AlignmentFlag.AlignRight)

        card_layout.addWidget(color_indicator)
        card_layout.addWidget(content, 1)

        return card

    def create_home_page(self):
        """إنشاء الصفحة الرئيسية"""
        content = QWidget()
        content_layout = QVBoxLayout(content)
        content_layout.setContentsMargins(20, 20, 20, 20)
        content_layout.setSpacing(20)

        # عنوان الوصول السريع
        quick_access_label = QLabel("الوصول السريع")
        quick_access_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
        """)
        quick_access_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # حقل البحث
        search_container = QWidget()
        search_layout = QHBoxLayout(search_container)
        search_layout.setContentsMargins(0, 0, 0, 0)

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("بحث...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                padding: 10px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
            }
        """)

        search_button = QPushButton("بحث")
        search_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLORS['light_blue']};
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-size: 14px;
            }}
            QPushButton:hover {{
                background-color: #166d96;
            }}
        """)
        search_button.clicked.connect(self.search_documents)

        search_layout.addWidget(search_button)
        search_layout.addWidget(self.search_input)

        # زر إضافة ملف جديد
        add_file_button = QPushButton("إضافة ملف جديد")
        add_file_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLORS['pink']};
                color: white;
                border: none;
                padding: 15px;
                border-radius: 4px;
                font-size: 16px;
                margin-top: 20px;
            }}
            QPushButton:hover {{
                background-color: #d81b60;
            }}
        """)
        add_file_button.clicked.connect(self.add_document)

        # إضافة العناصر إلى التخطيط
        content_layout.addWidget(quick_access_label)
        content_layout.addWidget(search_container)
        content_layout.addWidget(add_file_button)
        content_layout.addStretch(1)

        return content

    def show_login_window(self):
        """عرض نافذة تسجيل الدخول"""
        # التحقق مما إذا كان المستخدم قد سجل الدخول مسبقًا
        if self.current_user:
            return

        login_window = LoginWindow(on_login_success=self.on_login_success)
        login_window.show()

        # تعطيل الوصول إلى الصفحات التي تتطلب تسجيل الدخول
        self.disable_authenticated_pages()

    def on_login_success(self, user):
        """تم تسجيل الدخول بنجاح"""
        self.current_user = user
        self.user_info_label.setText(f"مرحبًا، {user['full_name'] or user['username']}")

        # تعيين المستخدم الحالي للصفحات
        self.documents_page.set_current_user(user)
        self.archive_page.set_current_user(user)

        # تمكين الوصول إلى جميع الصفحات
        self.enable_authenticated_pages()

        # تحديث الإحصائيات
        self.update_stats()

    def logout(self):
        """تسجيل الخروج"""
        self.current_user = None
        self.user_info_label.setText("مرحبًا، زائر")

        # العودة إلى الصفحة الرئيسية
        self.stacked_widget.setCurrentIndex(0)

        # تعطيل الوصول إلى الصفحات التي تتطلب تسجيل الدخول
        self.disable_authenticated_pages()

        # عرض نافذة تسجيل الدخول مرة أخرى
        self.show_login_window()

    def disable_authenticated_pages(self):
        """تعطيل الوصول إلى الصفحات التي تتطلب تسجيل الدخول"""
        # تعطيل أزرار القائمة للصفحات التي تتطلب تسجيل الدخول
        for i, btn in enumerate(self.menu_buttons):
            # الصفحة الرئيسية متاحة للجميع
            if i == 0:
                continue
            btn.setEnabled(False)
            btn.setStyleSheet(f"""
                QPushButton {{
                    color: #999999;
                    background-color: transparent;
                    border: none;
                    text-align: right;
                    padding: 15px 20px;
                    font-size: 14px;
                }}
                QPushButton:hover {{
                    background-color: rgba(255, 255, 255, 0.05);
                }}
            """)

    def enable_authenticated_pages(self):
        """تمكين الوصول إلى جميع الصفحات"""
        # تمكين جميع أزرار القائمة
        for btn in self.menu_buttons:
            btn.setEnabled(True)
            btn.setStyleSheet(f"""
                QPushButton {{
                    color: {COLORS['white']};
                    background-color: transparent;
                    border: none;
                    text-align: right;
                    padding: 15px 20px;
                    font-size: 14px;
                }}
                QPushButton:hover {{
                    background-color: rgba(255, 255, 255, 0.1);
                }}
            """)

    def update_stats(self):
        """تحديث الإحصائيات"""
        stats = self.db.get_stats()

        # تحديث بطاقات الإحصائيات
        self.stats_cards["departments"].value_label.setText(str(stats['departments_count']))
        self.stats_cards["users"].value_label.setText(str(stats['users_count']))
        self.stats_cards["documents"].value_label.setText(str(stats['documents_count']))

        # تنسيق حجم الملفات
        total_size = stats['total_size']
        if total_size < 1024:
            size_str = f"{total_size} بايت"
        elif total_size < 1024 * 1024:
            size_str = f"{total_size / 1024:.2f} كيلوبايت"
        else:
            size_str = f"{total_size / (1024 * 1024):.2f} ميجابايت"

        self.stats_cards["size"].value_label.setText(size_str)

    def search_documents(self):
        """البحث في المستندات"""
        search_text = self.search_input.text()
        if search_text:
            # الانتقال إلى صفحة المستندات
            self.stacked_widget.setCurrentIndex(2)

            # تنفيذ البحث
            self.documents_page.search_input.setText(search_text)
            self.documents_page.search_documents()

    def add_document(self):
        """إضافة مستند جديد"""
        if not self.current_user:
            QMessageBox.warning(self, "خطأ", "يجب تسجيل الدخول أولاً")
            return

        # الانتقال إلى صفحة المستندات
        self.stacked_widget.setCurrentIndex(2)

        # فتح نافذة إضافة مستند
        self.documents_page.add_document()


def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    app = QApplication(sys.argv)

    # تعيين اتجاه التطبيق من اليمين إلى اليسار للغة العربية
    app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

    # تعيين الخط للتطبيق
    font = QFont("Arial", 10)
    app.setFont(font)

    window = MainWindow()
    window.show()

    sys.exit(app.exec())


if __name__ == "__main__":
    main()
