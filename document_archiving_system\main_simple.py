#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
نسخة مبسطة من نظام أرشفة الكتب
"""

import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                            QWidget, QLabel, QPushButton, QLineEdit, QDialog,
                            QMessageBox, QStackedWidget)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class SimpleLoginDialog(QDialog):
    """نافذة تسجيل دخول بسيطة"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("تسجيل الدخول")
        self.setFixedSize(350, 250)
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # العنوان
        title = QLabel("نظام أرشفة الكتب")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("font-size: 18px; font-weight: bold; color: #1a3a63;")
        
        # اسم المستخدم
        self.username = QLineEdit()
        self.username.setPlaceholderText("اسم المستخدم")
        self.username.setText("admin")
        self.username.setStyleSheet("padding: 10px; border: 1px solid #ccc; border-radius: 5px;")
        
        # كلمة المرور
        self.password = QLineEdit()
        self.password.setPlaceholderText("كلمة المرور")
        self.password.setText("admin123")
        self.password.setEchoMode(QLineEdit.Password)
        self.password.setStyleSheet("padding: 10px; border: 1px solid #ccc; border-radius: 5px;")
        
        # زر الدخول
        login_btn = QPushButton("تسجيل الدخول")
        login_btn.setStyleSheet("""
            QPushButton {
                background-color: #1a3a63;
                color: white;
                padding: 10px;
                border: none;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2c5282;
            }
        """)
        login_btn.clicked.connect(self.login)
        
        layout.addWidget(title)
        layout.addWidget(QLabel("اسم المستخدم:"))
        layout.addWidget(self.username)
        layout.addWidget(QLabel("كلمة المرور:"))
        layout.addWidget(self.password)
        layout.addWidget(login_btn)
    
    def login(self):
        if self.username.text() == "admin" and self.password.text() == "admin123":
            self.accept()
        else:
            QMessageBox.warning(self, "خطأ", "بيانات خاطئة")

class MainWindow(QMainWindow):
    """النافذة الرئيسية المبسطة"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("نظام أرشفة الكتب الإلكترونية")
        self.setGeometry(100, 100, 800, 600)
        self.init_ui()
        
        # عرض نافذة تسجيل الدخول
        self.show_login()
    
    def init_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # شريط علوي
        header = QLabel("مرحباً بك في نظام أرشفة الكتب")
        header.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header.setStyleSheet("""
            QLabel {
                background-color: #1a3a63;
                color: white;
                padding: 20px;
                font-size: 20px;
                font-weight: bold;
            }
        """)
        
        # المحتوى
        content = QLabel("تم تسجيل الدخول بنجاح! 🎉")
        content.setAlignment(Qt.AlignmentFlag.AlignCenter)
        content.setStyleSheet("""
            QLabel {
                font-size: 16px;
                padding: 50px;
                background-color: #f0f8ff;
                border: 2px solid #1a3a63;
                border-radius: 10px;
                margin: 20px;
            }
        """)
        
        # أزرار
        buttons_layout = QHBoxLayout()
        
        books_btn = QPushButton("إدارة الكتب")
        books_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 15px;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        books_btn.clicked.connect(lambda: QMessageBox.information(self, "الكتب", "قسم إدارة الكتب"))
        
        settings_btn = QPushButton("الإعدادات")
        settings_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 15px;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        settings_btn.clicked.connect(lambda: QMessageBox.information(self, "الإعدادات", "قسم الإعدادات"))
        
        buttons_layout.addWidget(books_btn)
        buttons_layout.addWidget(settings_btn)
        
        layout.addWidget(header)
        layout.addWidget(content)
        layout.addLayout(buttons_layout)
    
    def show_login(self):
        """عرض نافذة تسجيل الدخول"""
        login_dialog = SimpleLoginDialog()
        if login_dialog.exec() == QDialog.Accepted:
            self.show()
        else:
            self.close()

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # تعيين الاتجاه والخط
    app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
    font = QFont("Arial", 10)
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    window = MainWindow()
    
    # تشغيل التطبيق
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
