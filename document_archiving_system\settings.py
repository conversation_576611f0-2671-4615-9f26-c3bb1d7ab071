#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
إعدادات البرنامج
تتعامل مع إعدادات البرنامج المختلفة
"""

import os
import json
import shutil
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, 
                            QLineEdit, QTabWidget, QFormLayout, QFileDialog, QMessageBox,
                            QCheckBox, QGroupBox, QSpinBox, QComboBox)
from PyQt5.QtGui import QIcon, QFont
from PyQt5.QtCore import Qt


class SettingsWidget(QWidget):
    """واجهة إعدادات البرنامج"""
    
    def __init__(self, db):
        super().__init__()
        
        # تهيئة قاعدة البيانات
        self.db = db
        
        # مسار ملف الإعدادات
        self.settings_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "settings.json")
        
        # تحميل الإعدادات
        self.settings = self.load_settings()
        
        # إنشاء واجهة المستخدم
        self.init_ui()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # عنوان الصفحة
        title_label = QLabel("إعدادات البرنامج")
        title_label.setStyleSheet("""
            font-size: 20px;
            font-weight: bold;
            color: #1a3a63;
        """)
        
        # إنشاء تبويبات الإعدادات
        tabs = QTabWidget()
        tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
                padding: 10px;
            }
            QTabBar::tab {
                background-color: #f5f5f5;
                border: 1px solid #ddd;
                border-bottom: none;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 1px solid white;
            }
        """)
        
        # إعدادات عامة
        general_tab = self.create_general_tab()
        
        # إعدادات المستندات
        documents_tab = self.create_documents_tab()
        
        # إعدادات النسخ الاحتياطي
        backup_tab = self.create_backup_tab()
        
        # إضافة التبويبات
        tabs.addTab(general_tab, "إعدادات عامة")
        tabs.addTab(documents_tab, "إعدادات المستندات")
        tabs.addTab(backup_tab, "النسخ الاحتياطي")
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        save_button = QPushButton("حفظ الإعدادات")
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #16a085;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #138a72;
            }
        """)
        save_button.clicked.connect(self.save_settings_to_file)
        
        reset_button = QPushButton("استعادة الإعدادات الافتراضية")
        reset_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        reset_button.clicked.connect(self.reset_settings)
        
        buttons_layout.addWidget(save_button)
        buttons_layout.addWidget(reset_button)
        
        # إضافة العناصر إلى التخطيط الرئيسي
        main_layout.addWidget(title_label)
        main_layout.addWidget(tabs)
        main_layout.addLayout(buttons_layout)
    
    def create_general_tab(self):
        """إنشاء تبويب الإعدادات العامة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # مجموعة إعدادات الواجهة
        ui_group = QGroupBox("إعدادات الواجهة")
        ui_layout = QFormLayout(ui_group)
        
        # اتجاه الواجهة
        self.rtl_checkbox = QCheckBox("واجهة من اليمين إلى اليسار")
        self.rtl_checkbox.setChecked(self.settings.get("rtl", True))
        
        # لغة الواجهة
        self.language_combo = QComboBox()
        self.language_combo.addItem("العربية", "ar")
        self.language_combo.addItem("English", "en")
        
        # تعيين اللغة الحالية
        lang_index = 0 if self.settings.get("language", "ar") == "ar" else 1
        self.language_combo.setCurrentIndex(lang_index)
        
        ui_layout.addRow("اتجاه الواجهة:", self.rtl_checkbox)
        ui_layout.addRow("لغة الواجهة:", self.language_combo)
        
        # مجموعة إعدادات المستخدم
        user_group = QGroupBox("إعدادات المستخدم")
        user_layout = QFormLayout(user_group)
        
        # تذكر المستخدم
        self.remember_user_checkbox = QCheckBox("تذكر المستخدم")
        self.remember_user_checkbox.setChecked(self.settings.get("remember_user", False))
        
        # تسجيل الخروج التلقائي
        self.auto_logout_checkbox = QCheckBox("تسجيل الخروج التلقائي بعد فترة من عدم النشاط")
        self.auto_logout_checkbox.setChecked(self.settings.get("auto_logout", False))
        
        # فترة عدم النشاط (بالدقائق)
        self.inactivity_period = QSpinBox()
        self.inactivity_period.setMinimum(1)
        self.inactivity_period.setMaximum(60)
        self.inactivity_period.setValue(self.settings.get("inactivity_period", 15))
        
        user_layout.addRow("تذكر المستخدم:", self.remember_user_checkbox)
        user_layout.addRow("تسجيل الخروج التلقائي:", self.auto_logout_checkbox)
        user_layout.addRow("فترة عدم النشاط (بالدقائق):", self.inactivity_period)
        
        # إضافة المجموعات إلى التخطيط
        layout.addWidget(ui_group)
        layout.addWidget(user_group)
        layout.addStretch(1)
        
        return tab
    
    def create_documents_tab(self):
        """إنشاء تبويب إعدادات المستندات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # مجموعة إعدادات التخزين
        storage_group = QGroupBox("إعدادات التخزين")
        storage_layout = QFormLayout(storage_group)
        
        # مسار تخزين المستندات
        self.storage_path_input = QLineEdit()
        self.storage_path_input.setText(self.settings.get("storage_path", "documents"))
        self.storage_path_input.setReadOnly(True)
        
        storage_path_layout = QHBoxLayout()
        storage_path_layout.addWidget(self.storage_path_input)
        
        browse_button = QPushButton("استعراض...")
        browse_button.clicked.connect(self.browse_storage_path)
        storage_path_layout.addWidget(browse_button)
        
        # تنظيم المستندات حسب القسم
        self.organize_by_department = QCheckBox("تنظيم المستندات في مجلدات حسب القسم")
        self.organize_by_department.setChecked(self.settings.get("organize_by_department", True))
        
        # تنظيم المستندات حسب التاريخ
        self.organize_by_date = QCheckBox("تنظيم المستندات في مجلدات حسب التاريخ")
        self.organize_by_date.setChecked(self.settings.get("organize_by_date", False))
        
        storage_layout.addRow("مسار تخزين المستندات:", storage_path_layout)
        storage_layout.addRow("تنظيم المستندات:", self.organize_by_department)
        storage_layout.addRow("", self.organize_by_date)
        
        # مجموعة إعدادات الملفات
        files_group = QGroupBox("إعدادات الملفات")
        files_layout = QFormLayout(files_group)
        
        # أنواع الملفات المسموح بها
        self.allowed_extensions_input = QLineEdit()
        self.allowed_extensions_input.setText(self.settings.get("allowed_extensions", "pdf,doc,docx,jpg,jpeg,png"))
        self.allowed_extensions_input.setPlaceholderText("أدخل امتدادات الملفات مفصولة بفواصل")
        
        # الحد الأقصى لحجم الملف (بالميجابايت)
        self.max_file_size = QSpinBox()
        self.max_file_size.setMinimum(1)
        self.max_file_size.setMaximum(1000)
        self.max_file_size.setValue(self.settings.get("max_file_size", 50))
        
        files_layout.addRow("أنواع الملفات المسموح بها:", self.allowed_extensions_input)
        files_layout.addRow("الحد الأقصى لحجم الملف (ميجابايت):", self.max_file_size)
        
        # إضافة المجموعات إلى التخطيط
        layout.addWidget(storage_group)
        layout.addWidget(files_group)
        layout.addStretch(1)
        
        return tab
    
    def create_backup_tab(self):
        """إنشاء تبويب النسخ الاحتياطي"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # مجموعة إعدادات النسخ الاحتياطي
        backup_group = QGroupBox("إعدادات النسخ الاحتياطي")
        backup_layout = QFormLayout(backup_group)
        
        # تفعيل النسخ الاحتياطي التلقائي
        self.auto_backup_checkbox = QCheckBox("تفعيل النسخ الاحتياطي التلقائي")
        self.auto_backup_checkbox.setChecked(self.settings.get("auto_backup", False))
        
        # فترة النسخ الاحتياطي (بالأيام)
        self.backup_period = QSpinBox()
        self.backup_period.setMinimum(1)
        self.backup_period.setMaximum(30)
        self.backup_period.setValue(self.settings.get("backup_period", 7))
        
        # مسار النسخ الاحتياطي
        self.backup_path_input = QLineEdit()
        self.backup_path_input.setText(self.settings.get("backup_path", "backups"))
        self.backup_path_input.setReadOnly(True)
        
        backup_path_layout = QHBoxLayout()
        backup_path_layout.addWidget(self.backup_path_input)
        
        browse_backup_button = QPushButton("استعراض...")
        browse_backup_button.clicked.connect(self.browse_backup_path)
        backup_path_layout.addWidget(browse_backup_button)
        
        # عدد النسخ الاحتياطية للاحتفاظ بها
        self.backup_count = QSpinBox()
        self.backup_count.setMinimum(1)
        self.backup_count.setMaximum(10)
        self.backup_count.setValue(self.settings.get("backup_count", 3))
        
        backup_layout.addRow("النسخ الاحتياطي التلقائي:", self.auto_backup_checkbox)
        backup_layout.addRow("فترة النسخ الاحتياطي (بالأيام):", self.backup_period)
        backup_layout.addRow("مسار النسخ الاحتياطي:", backup_path_layout)
        backup_layout.addRow("عدد النسخ الاحتياطية للاحتفاظ بها:", self.backup_count)
        
        # أزرار النسخ الاحتياطي
        backup_buttons_layout = QHBoxLayout()
        
        create_backup_button = QPushButton("إنشاء نسخة احتياطية الآن")
        create_backup_button.setStyleSheet("""
            QPushButton {
                background-color: #1e81b0;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #166d96;
            }
        """)
        create_backup_button.clicked.connect(self.create_backup)
        
        restore_backup_button = QPushButton("استعادة من نسخة احتياطية")
        restore_backup_button.setStyleSheet("""
            QPushButton {
                background-color: #e91e63;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #d81b60;
            }
        """)
        restore_backup_button.clicked.connect(self.restore_backup)
        
        backup_buttons_layout.addWidget(create_backup_button)
        backup_buttons_layout.addWidget(restore_backup_button)
        
        # إضافة المجموعات إلى التخطيط
        layout.addWidget(backup_group)
        layout.addLayout(backup_buttons_layout)
        layout.addStretch(1)
        
        return tab
    
    def browse_storage_path(self):
        """اختيار مسار تخزين المستندات"""
        directory = QFileDialog.getExistingDirectory(self, "اختر مسار تخزين المستندات")
        if directory:
            self.storage_path_input.setText(directory)
    
    def browse_backup_path(self):
        """اختيار مسار النسخ الاحتياطي"""
        directory = QFileDialog.getExistingDirectory(self, "اختر مسار النسخ الاحتياطي")
        if directory:
            self.backup_path_input.setText(directory)
    
    def load_settings(self):
        """تحميل الإعدادات من الملف"""
        default_settings = {
            "rtl": True,
            "language": "ar",
            "remember_user": False,
            "auto_logout": False,
            "inactivity_period": 15,
            "storage_path": "documents",
            "organize_by_department": True,
            "organize_by_date": False,
            "allowed_extensions": "pdf,doc,docx,jpg,jpeg,png",
            "max_file_size": 50,
            "auto_backup": False,
            "backup_period": 7,
            "backup_path": "backups",
            "backup_count": 3
        }
        
        if os.path.exists(self.settings_path):
            try:
                with open(self.settings_path, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                return {**default_settings, **settings}
            except:
                return default_settings
        else:
            return default_settings
    
    def save_settings_to_file(self):
        """حفظ الإعدادات إلى ملف"""
        settings = {
            "rtl": self.rtl_checkbox.isChecked(),
            "language": self.language_combo.currentData(),
            "remember_user": self.remember_user_checkbox.isChecked(),
            "auto_logout": self.auto_logout_checkbox.isChecked(),
            "inactivity_period": self.inactivity_period.value(),
            "storage_path": self.storage_path_input.text(),
            "organize_by_department": self.organize_by_department.isChecked(),
            "organize_by_date": self.organize_by_date.isChecked(),
            "allowed_extensions": self.allowed_extensions_input.text(),
            "max_file_size": self.max_file_size.value(),
            "auto_backup": self.auto_backup_checkbox.isChecked(),
            "backup_period": self.backup_period.value(),
            "backup_path": self.backup_path_input.text(),
            "backup_count": self.backup_count.value()
        }
        
        try:
            with open(self.settings_path, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=4)
            
            QMessageBox.information(self, "نجاح", "تم حفظ الإعدادات بنجاح")
            
            # تحديث الإعدادات
            self.settings = settings
            
            # تطبيق الإعدادات
            self.apply_settings()
            
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في حفظ الإعدادات: {str(e)}")
    
    def reset_settings(self):
        """استعادة الإعدادات الافتراضية"""
        reply = QMessageBox.question(
            self, 
            "تأكيد", 
            "هل أنت متأكد من استعادة الإعدادات الافتراضية؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No, 
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # حذف ملف الإعدادات إذا كان موجودًا
            if os.path.exists(self.settings_path):
                os.remove(self.settings_path)
            
            # إعادة تحميل الإعدادات
            self.settings = self.load_settings()
            
            # تحديث واجهة المستخدم
            self.rtl_checkbox.setChecked(self.settings["rtl"])
            self.language_combo.setCurrentIndex(0 if self.settings["language"] == "ar" else 1)
            self.remember_user_checkbox.setChecked(self.settings["remember_user"])
            self.auto_logout_checkbox.setChecked(self.settings["auto_logout"])
            self.inactivity_period.setValue(self.settings["inactivity_period"])
            self.storage_path_input.setText(self.settings["storage_path"])
            self.organize_by_department.setChecked(self.settings["organize_by_department"])
            self.organize_by_date.setChecked(self.settings["organize_by_date"])
            self.allowed_extensions_input.setText(self.settings["allowed_extensions"])
            self.max_file_size.setValue(self.settings["max_file_size"])
            self.auto_backup_checkbox.setChecked(self.settings["auto_backup"])
            self.backup_period.setValue(self.settings["backup_period"])
            self.backup_path_input.setText(self.settings["backup_path"])
            self.backup_count.setValue(self.settings["backup_count"])
            
            QMessageBox.information(self, "نجاح", "تم استعادة الإعدادات الافتراضية بنجاح")
            
            # تطبيق الإعدادات
            self.apply_settings()
    
    def apply_settings(self):
        """تطبيق الإعدادات"""
        # يمكن إضافة كود هنا لتطبيق الإعدادات على البرنامج
        pass
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        # التأكد من وجود مسار النسخ الاحتياطي
        backup_path = self.backup_path_input.text()
        if not os.path.exists(backup_path):
            try:
                os.makedirs(backup_path)
            except Exception as e:
                QMessageBox.warning(self, "خطأ", f"فشل في إنشاء مسار النسخ الاحتياطي: {str(e)}")
                return
        
        # إنشاء اسم ملف النسخة الاحتياطية
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = os.path.join(backup_path, f"backup_{timestamp}.zip")
        
        try:
            # إغلاق الاتصال بقاعدة البيانات
            self.db.close()
            
            # إنشاء النسخة الاحتياطية
            import zipfile
            with zipfile.ZipFile(backup_file, 'w') as zipf:
                # إضافة ملف قاعدة البيانات
                db_path = self.db.db_path
                if os.path.exists(db_path):
                    zipf.write(db_path, os.path.basename(db_path))
                
                # إضافة مجلد المستندات
                storage_path = self.storage_path_input.text()
                if os.path.exists(storage_path):
                    for root, dirs, files in os.walk(storage_path):
                        for file in files:
                            file_path = os.path.join(root, file)
                            zipf.write(file_path, os.path.relpath(file_path, os.path.dirname(storage_path)))
            
            # إعادة فتح الاتصال بقاعدة البيانات
            self.db.connect()
            
            QMessageBox.information(self, "نجاح", f"تم إنشاء النسخة الاحتياطية بنجاح في:\n{backup_file}")
            
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في إنشاء النسخة الاحتياطية: {str(e)}")
            # إعادة فتح الاتصال بقاعدة البيانات في حالة الفشل
            self.db.connect()
    
    def restore_backup(self):
        """استعادة من نسخة احتياطية"""
        # اختيار ملف النسخة الاحتياطية
        backup_file, _ = QFileDialog.getOpenFileName(
            self,
            "اختر ملف النسخة الاحتياطية",
            self.backup_path_input.text(),
            "ملفات النسخ الاحتياطي (*.zip)"
        )
        
        if not backup_file:
            return
        
        # تأكيد الاستعادة
        reply = QMessageBox.question(
            self, 
            "تأكيد", 
            "سيتم استبدال البيانات الحالية بالبيانات من النسخة الاحتياطية. هل أنت متأكد من المتابعة؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No, 
            QMessageBox.StandardButton.No
        )
        
        if reply != QMessageBox.StandardButton.Yes:
            return
        
        try:
            # إغلاق الاتصال بقاعدة البيانات
            self.db.close()
            
            # استعادة النسخة الاحتياطية
            import zipfile
            import tempfile
            
            # إنشاء مجلد مؤقت
            temp_dir = tempfile.mkdtemp()
            
            # استخراج الملفات إلى المجلد المؤقت
            with zipfile.ZipFile(backup_file, 'r') as zipf:
                zipf.extractall(temp_dir)
            
            # نسخ ملف قاعدة البيانات
            db_filename = os.path.basename(self.db.db_path)
            temp_db_path = os.path.join(temp_dir, db_filename)
            if os.path.exists(temp_db_path):
                shutil.copy2(temp_db_path, self.db.db_path)
            
            # نسخ مجلد المستندات
            storage_path = self.storage_path_input.text()
            if os.path.exists(storage_path):
                # حذف المجلد الحالي
                shutil.rmtree(storage_path)
            
            # إنشاء مجلد المستندات
            os.makedirs(storage_path, exist_ok=True)
            
            # نسخ الملفات من المجلد المؤقت إلى مجلد المستندات
            for item in os.listdir(temp_dir):
                if item != db_filename:
                    s = os.path.join(temp_dir, item)
                    d = os.path.join(storage_path, item)
                    if os.path.isdir(s):
                        shutil.copytree(s, d, dirs_exist_ok=True)
                    else:
                        shutil.copy2(s, d)
            
            # حذف المجلد المؤقت
            shutil.rmtree(temp_dir)
            
            # إعادة فتح الاتصال بقاعدة البيانات
            self.db.connect()
            
            QMessageBox.information(self, "نجاح", "تم استعادة النسخة الاحتياطية بنجاح")
            
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في استعادة النسخة الاحتياطية: {str(e)}")
            # إعادة فتح الاتصال بقاعدة البيانات في حالة الفشل
            self.db.connect()
