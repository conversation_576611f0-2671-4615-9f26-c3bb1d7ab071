#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
إدارة المستخدمين
تتعامل مع عرض وإضافة وتعديل المستخدمين
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, 
                            QLineEdit, QTableWidget, QTableWidgetItem, QHeaderView,
                            QMessageBox, QDialog, QFormLayout, QComboBox)
from PyQt5.QtGui import QIcon, QFont
from PyQt5.QtCore import Qt


class UsersWidget(QWidget):
    """واجهة إدارة المستخدمين"""
    
    def __init__(self, db):
        super().__init__()
        
        # تهيئة قاعدة البيانات
        self.db = db
        
        # إنشاء واجهة المستخدم
        self.init_ui()
        
        # تحميل المستخدمين
        self.load_users()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # عنوان الصفحة
        title_label = QLabel("إدارة المستخدمين")
        title_label.setStyleSheet("""
            font-size: 20px;
            font-weight: bold;
            color: #1a3a63;
        """)
        
        # شريط الأدوات
        toolbar_layout = QHBoxLayout()
        
        # زر إضافة مستخدم جديد
        add_button = QPushButton("إضافة مستخدم جديد")
        add_button.setStyleSheet("""
            QPushButton {
                background-color: #16a085;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #138a72;
            }
        """)
        add_button.clicked.connect(self.add_user)
        
        # حقل البحث
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("بحث في المستخدمين...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
            }
        """)
        self.search_input.textChanged.connect(self.filter_users)
        
        toolbar_layout.addWidget(add_button)
        toolbar_layout.addStretch(1)
        toolbar_layout.addWidget(self.search_input)
        
        # جدول المستخدمين
        self.users_table = QTableWidget()
        self.users_table.setColumnCount(5)
        self.users_table.setHorizontalHeaderLabels(["المعرف", "اسم المستخدم", "الاسم الكامل", "الصلاحية", "الإجراءات"])
        self.users_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.users_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Stretch)
        self.users_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }
            QHeaderView::section {
                background-color: #1a3a63;
                color: white;
                padding: 8px;
                font-size: 14px;
                font-weight: bold;
                border: none;
            }
        """)
        
        # إضافة العناصر إلى التخطيط الرئيسي
        main_layout.addWidget(title_label)
        main_layout.addLayout(toolbar_layout)
        main_layout.addWidget(self.users_table)
    
    def load_users(self):
        """تحميل المستخدمين من قاعدة البيانات"""
        users = self.db.get_users()
        
        # تفريغ الجدول
        self.users_table.setRowCount(0)
        
        # إضافة المستخدمين إلى الجدول
        for row, user in enumerate(users):
            self.users_table.insertRow(row)
            
            # إضافة بيانات المستخدم
            id_item = QTableWidgetItem(str(user['id']))
            username_item = QTableWidgetItem(user['username'])
            full_name_item = QTableWidgetItem(user['full_name'] or "")
            role_item = QTableWidgetItem(self.translate_role(user['role']))
            
            self.users_table.setItem(row, 0, id_item)
            self.users_table.setItem(row, 1, username_item)
            self.users_table.setItem(row, 2, full_name_item)
            self.users_table.setItem(row, 3, role_item)
            
            # إضافة أزرار الإجراءات
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(0, 0, 0, 0)
            
            edit_button = QPushButton("تعديل")
            edit_button.setStyleSheet("""
                QPushButton {
                    background-color: #1e81b0;
                    color: white;
                    border: none;
                    padding: 5px 10px;
                    border-radius: 3px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #166d96;
                }
            """)
            
            delete_button = QPushButton("حذف")
            delete_button.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border: none;
                    padding: 5px 10px;
                    border-radius: 3px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
            """)
            
            # ربط الأزرار بالإجراءات
            user_id = user['id']
            edit_button.clicked.connect(lambda _, user_id=user_id: self.edit_user(user_id))
            delete_button.clicked.connect(lambda _, user_id=user_id: self.delete_user(user_id))
            
            actions_layout.addWidget(edit_button)
            actions_layout.addWidget(delete_button)
            
            self.users_table.setCellWidget(row, 4, actions_widget)
    
    def translate_role(self, role):
        """ترجمة دور المستخدم إلى العربية"""
        roles = {
            "admin": "مدير",
            "user": "مستخدم"
        }
        return roles.get(role, role)
    
    def filter_users(self):
        """تصفية المستخدمين حسب البحث"""
        search_text = self.search_input.text().lower()
        
        for row in range(self.users_table.rowCount()):
            username_item = self.users_table.item(row, 1)
            full_name_item = self.users_table.item(row, 2)
            
            if (username_item and search_text in username_item.text().lower()) or \
               (full_name_item and search_text in full_name_item.text().lower()):
                self.users_table.setRowHidden(row, False)
            else:
                self.users_table.setRowHidden(row, True)
    
    def add_user(self):
        """إضافة مستخدم جديد"""
        dialog = UserDialog(self)
        if dialog.exec() == QDialog.Accepted:
            username = dialog.username_input.text()
            password = dialog.password_input.text()
            full_name = dialog.full_name_input.text()
            role = dialog.role_combo.currentData()
            
            if username and password:
                user_id = self.db.add_user(username, password, full_name, "", role)
                if user_id:
                    self.load_users()
                    QMessageBox.information(self, "نجاح", f"تم إضافة المستخدم '{username}' بنجاح")
                else:
                    QMessageBox.warning(self, "خطأ", "فشل في إضافة المستخدم")
    
    def edit_user(self, user_id):
        """تعديل مستخدم"""
        # استرجاع بيانات المستخدم
        self.db.cursor.execute("SELECT * FROM users WHERE id = ?", (user_id,))
        user = self.db.cursor.fetchone()
        
        if not user:
            QMessageBox.warning(self, "خطأ", "المستخدم غير موجود")
            return
        
        dialog = UserDialog(self, user=dict(user))
        if dialog.exec() == QDialog.Accepted:
            username = dialog.username_input.text()
            password = dialog.password_input.text()
            full_name = dialog.full_name_input.text()
            role = dialog.role_combo.currentData()
            
            if username:
                try:
                    # إذا كانت كلمة المرور فارغة، لا تقم بتحديثها
                    if password:
                        self.db.cursor.execute(
                            "UPDATE users SET username = ?, password = ?, full_name = ?, role = ? WHERE id = ?",
                            (username, password, full_name, role, user_id)
                        )
                    else:
                        self.db.cursor.execute(
                            "UPDATE users SET username = ?, full_name = ?, role = ? WHERE id = ?",
                            (username, full_name, role, user_id)
                        )
                    
                    self.db.conn.commit()
                    self.load_users()
                    QMessageBox.information(self, "نجاح", f"تم تعديل المستخدم '{username}' بنجاح")
                except Exception as e:
                    QMessageBox.warning(self, "خطأ", f"فشل في تعديل المستخدم: {str(e)}")
    
    def delete_user(self, user_id):
        """حذف مستخدم"""
        # التحقق من وجود مستندات مرتبطة بالمستخدم
        self.db.cursor.execute("SELECT COUNT(*) FROM documents WHERE created_by = ?", (user_id,))
        documents_count = self.db.cursor.fetchone()[0]
        
        if documents_count > 0:
            QMessageBox.warning(
                self, 
                "تحذير", 
                f"لا يمكن حذف هذا المستخدم لأنه مرتبط بـ {documents_count} مستند."
            )
            return
        
        # تأكيد الحذف
        reply = QMessageBox.question(
            self, 
            "تأكيد الحذف", 
            "هل أنت متأكد من حذف هذا المستخدم؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No, 
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            try:
                self.db.cursor.execute("DELETE FROM users WHERE id = ?", (user_id,))
                self.db.conn.commit()
                self.load_users()
                QMessageBox.information(self, "نجاح", "تم حذف المستخدم بنجاح")
            except Exception as e:
                QMessageBox.warning(self, "خطأ", f"فشل في حذف المستخدم: {str(e)}")


class UserDialog(QDialog):
    """نافذة حوار إضافة/تعديل مستخدم"""
    
    def __init__(self, parent=None, user=None):
        super().__init__(parent)
        
        # تعيين عنوان النافذة
        self.setWindowTitle("إضافة مستخدم جديد" if not user else "تعديل مستخدم")
        self.setFixedSize(400, 300)
        
        # إنشاء واجهة المستخدم
        self.init_ui(user)
    
    def init_ui(self, user=None):
        """تهيئة واجهة المستخدم"""
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        
        # نموذج البيانات
        form_layout = QFormLayout()
        
        # حقل اسم المستخدم
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        self.username_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
            }
        """)
        
        # حقل كلمة المرور
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("أدخل كلمة المرور" if not user else "اتركها فارغة للاحتفاظ بكلمة المرور الحالية")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
            }
        """)
        
        # حقل الاسم الكامل
        self.full_name_input = QLineEdit()
        self.full_name_input.setPlaceholderText("أدخل الاسم الكامل (اختياري)")
        self.full_name_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
            }
        """)
        
        # قائمة الصلاحيات
        self.role_combo = QComboBox()
        self.role_combo.addItem("مستخدم", "user")
        self.role_combo.addItem("مدير", "admin")
        self.role_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
            }
        """)
        
        # إضافة الحقول إلى النموذج
        form_layout.addRow("اسم المستخدم:", self.username_input)
        form_layout.addRow("كلمة المرور:", self.password_input)
        form_layout.addRow("الاسم الكامل:", self.full_name_input)
        form_layout.addRow("الصلاحية:", self.role_combo)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        save_button = QPushButton("حفظ")
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #16a085;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #138a72;
            }
        """)
        save_button.clicked.connect(self.accept)
        
        cancel_button = QPushButton("إلغاء")
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        cancel_button.clicked.connect(self.reject)
        
        buttons_layout.addWidget(save_button)
        buttons_layout.addWidget(cancel_button)
        
        # إضافة العناصر إلى التخطيط الرئيسي
        main_layout.addLayout(form_layout)
        main_layout.addStretch(1)
        main_layout.addLayout(buttons_layout)
        
        # تعبئة البيانات إذا كان هناك مستخدم للتعديل
        if user:
            self.username_input.setText(user['username'])
            self.full_name_input.setText(user['full_name'] or "")
            
            # تعيين الصلاحية
            role_index = 0 if user['role'] == "user" else 1
            self.role_combo.setCurrentIndex(role_index)
