#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
اختبار بسيط لنافذة تسجيل الدخول
"""

import sys
from PyQt5.QtWidgets import QApplication, QDialog, QVBoxLayout, QLabel, QLineEdit, QPushButton, QMessageBox
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class SimpleLoginWindow(QDialog):
    """نافذة تسجيل دخول بسيطة للاختبار"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("تسجيل الدخول - اختبار")
        self.setFixedSize(400, 300)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # العنوان
        title = QLabel("نظام أرشفة الكتب")
        title.setStyleSheet("font-size: 18px; font-weight: bold; color: #1a3a63;")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # اسم المستخدم
        username_label = QLabel("اسم المستخدم:")
        username_label.setStyleSheet("font-weight: bold; color: #333;")
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        self.username_input.setText("admin")
        self.username_input.setStyleSheet("""
            QLineEdit {
                padding: 10px;
                border: 2px solid #ddd;
                border-radius: 5px;
                font-size: 14px;
            }
            QLineEdit:focus {
                border: 2px solid #1a3a63;
            }
        """)
        
        # كلمة المرور
        password_label = QLabel("كلمة المرور:")
        password_label.setStyleSheet("font-weight: bold; color: #333;")
        
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setText("admin123")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setStyleSheet("""
            QLineEdit {
                padding: 10px;
                border: 2px solid #ddd;
                border-radius: 5px;
                font-size: 14px;
            }
            QLineEdit:focus {
                border: 2px solid #1a3a63;
            }
        """)
        
        # زر تسجيل الدخول
        login_button = QPushButton("تسجيل الدخول")
        login_button.setStyleSheet("""
            QPushButton {
                background-color: #1a3a63;
                color: white;
                border: none;
                padding: 12px;
                border-radius: 5px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2c5282;
            }
        """)
        login_button.clicked.connect(self.login)
        
        # إضافة العناصر
        layout.addWidget(title)
        layout.addWidget(username_label)
        layout.addWidget(self.username_input)
        layout.addWidget(password_label)
        layout.addWidget(self.password_input)
        layout.addWidget(login_button)
    
    def login(self):
        """تسجيل الدخول"""
        username = self.username_input.text()
        password = self.password_input.text()
        
        if username == "admin" and password == "admin123":
            QMessageBox.information(self, "نجح", "تم تسجيل الدخول بنجاح!")
            self.accept()
        else:
            QMessageBox.warning(self, "خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
    
    font = QFont("Arial", 10)
    app.setFont(font)
    
    window = SimpleLoginWindow()
    if window.exec() == QDialog.Accepted:
        print("تم تسجيل الدخول بنجاح!")
    else:
        print("تم إلغاء تسجيل الدخول")
    
    sys.exit()

if __name__ == "__main__":
    main()
