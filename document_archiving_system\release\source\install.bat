@echo off
echo ========================================
echo   تثبيت نظام أرشفة الكتب الإلكترونية
echo   الإصدار 1.0.0
echo ========================================
echo.

echo فحص Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Python غير مثبت!
    echo يرجى تثبيت Python 3.7 أو أحدث من python.org
    pause
    exit /b 1
)

echo جاري تثبيت المتطلبات الأساسية...
pip install PyQt5 Pillow python-dateutil

echo جاري تثبيت المتطلبات الإضافية...
pip install requests

echo جاري تثبيت جميع المتطلبات من الملف...
pip install -r requirements.txt

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo   تم التثبيت بنجاح!
    echo ========================================
    echo.
    echo يمكنك الآن تشغيل البرنامج بالطرق التالية:
    echo 1. تشغيل run.bat
    echo 2. تشغيل python main.py
    echo.
    echo بيانات الدخول الافتراضية:
    echo اسم المستخدم: admin
    echo كلمة المرور: admin123
    echo.
    echo ملاحظة: إذا واجهت مشاكل مع التحديثات،
    echo فهذا طبيعي لأن مكتبة requests قد تحتاج إعداد إضافي.
    echo.
) else (
    echo.
    echo ========================================
    echo   فشل في التثبيت!
    echo ========================================
    echo.
    echo تأكد من:
    echo 1. تثبيت Python 3.7 أو أحدث
    echo 2. الاتصال بالإنترنت
    echo 3. صلاحيات المدير
    echo 4. تشغيل Command Prompt كمدير
    echo.
)

pause
