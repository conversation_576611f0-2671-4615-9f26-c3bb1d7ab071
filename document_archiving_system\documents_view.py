#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
عرض المستندات
تتعامل مع عرض وإدارة المستندات
"""

import os
import subprocess
from datetime import datetime

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, 
                            QLineEdit, QTableWidget, QTableWidgetItem, QHeaderView,
                            QMessageBox, QDialog, QFormLayout, QFileDialog, QComboBox,
                            QTextEdit, QProgressBar, QFrame)
from PyQt5.QtGui import QIcon, QFont, QPixmap
from PyQt5.QtCore import Qt, QSize, QThread, pyqtSignal

from document_manager import DocumentManager


class DocumentsWidget(QWidget):
    """واجهة عرض المستندات"""
    
    def __init__(self, db):
        super().__init__()
        
        # تهيئة قاعدة البيانات ومدير المستندات
        self.db = db
        self.document_manager = DocumentManager(db)
        
        # المستخدم الحالي
        self.current_user = None
        
        # إنشاء واجهة المستخدم
        self.init_ui()
        
        # تحميل المستندات
        self.load_documents()
    
    def set_current_user(self, user):
        """تعيين المستخدم الحالي"""
        self.current_user = user
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # عنوان الصفحة
        title_label = QLabel("إدارة المستندات")
        title_label.setStyleSheet("""
            font-size: 20px;
            font-weight: bold;
            color: #1a3a63;
        """)
        
        # شريط الأدوات
        toolbar_layout = QHBoxLayout()
        
        # زر إضافة مستند جديد
        add_button = QPushButton("إضافة مستند جديد")
        add_button.setStyleSheet("""
            QPushButton {
                background-color: #e91e63;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #d81b60;
            }
        """)
        add_button.clicked.connect(self.add_document)
        
        # قائمة الأقسام
        self.department_filter = QComboBox()
        self.department_filter.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
                min-width: 150px;
            }
        """)
        self.department_filter.addItem("جميع الأقسام", None)
        
        # إضافة الأقسام إلى القائمة
        departments = self.db.get_departments()
        for department in departments:
            self.department_filter.addItem(department['name'], department['id'])
        
        self.department_filter.currentIndexChanged.connect(self.filter_documents)
        
        # حقل البحث
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("بحث في المستندات...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
            }
        """)
        self.search_input.textChanged.connect(self.search_documents)
        
        search_button = QPushButton("بحث")
        search_button.setStyleSheet("""
            QPushButton {
                background-color: #1e81b0;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #166d96;
            }
        """)
        search_button.clicked.connect(self.search_documents)
        
        toolbar_layout.addWidget(add_button)
        toolbar_layout.addWidget(self.department_filter)
        toolbar_layout.addStretch(1)
        toolbar_layout.addWidget(self.search_input)
        toolbar_layout.addWidget(search_button)
        
        # جدول المستندات
        self.documents_table = QTableWidget()
        self.documents_table.setColumnCount(6)
        self.documents_table.setHorizontalHeaderLabels(["المعرف", "العنوان", "القسم", "الحجم", "تاريخ الإضافة", "الإجراءات"])
        self.documents_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.documents_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.documents_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)
        self.documents_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)
        self.documents_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }
            QHeaderView::section {
                background-color: #1a3a63;
                color: white;
                padding: 8px;
                font-size: 14px;
                font-weight: bold;
                border: none;
            }
        """)
        
        # إضافة العناصر إلى التخطيط الرئيسي
        main_layout.addWidget(title_label)
        main_layout.addLayout(toolbar_layout)
        main_layout.addWidget(self.documents_table)
    
    def load_documents(self, department_id=None):
        """تحميل المستندات من قاعدة البيانات"""
        if department_id:
            result = self.document_manager.get_documents_by_department(department_id)
        else:
            result = self.document_manager.get_all_documents()
        
        if not result['success']:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل المستندات: {result.get('error', '')}")
            return
        
        documents = result['documents']
        
        # تفريغ الجدول
        self.documents_table.setRowCount(0)
        
        # إضافة المستندات إلى الجدول
        for row, document in enumerate(documents):
            self.documents_table.insertRow(row)
            
            # إضافة بيانات المستند
            id_item = QTableWidgetItem(str(document['id']))
            title_item = QTableWidgetItem(document['title'])
            
            # الحصول على اسم القسم
            department_name = "غير محدد"
            if document['department_id']:
                self.db.cursor.execute("SELECT name FROM departments WHERE id = ?", (document['department_id'],))
                department = self.db.cursor.fetchone()
                if department:
                    department_name = department['name']
            
            department_item = QTableWidgetItem(department_name)
            
            # تنسيق حجم الملف
            file_size = document['file_size']
            if file_size < 1024:
                size_str = f"{file_size} بايت"
            elif file_size < 1024 * 1024:
                size_str = f"{file_size / 1024:.2f} كيلوبايت"
            else:
                size_str = f"{file_size / (1024 * 1024):.2f} ميجابايت"
            
            size_item = QTableWidgetItem(size_str)
            
            # تنسيق التاريخ
            created_at = datetime.strptime(document['created_at'], "%Y-%m-%d %H:%M:%S")
            date_str = created_at.strftime("%Y/%m/%d %H:%M")
            date_item = QTableWidgetItem(date_str)
            
            self.documents_table.setItem(row, 0, id_item)
            self.documents_table.setItem(row, 1, title_item)
            self.documents_table.setItem(row, 2, department_item)
            self.documents_table.setItem(row, 3, size_item)
            self.documents_table.setItem(row, 4, date_item)
            
            # إضافة أزرار الإجراءات
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(0, 0, 0, 0)
            
            view_button = QPushButton("عرض")
            view_button.setStyleSheet("""
                QPushButton {
                    background-color: #1e81b0;
                    color: white;
                    border: none;
                    padding: 5px 10px;
                    border-radius: 3px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #166d96;
                }
            """)
            
            delete_button = QPushButton("حذف")
            delete_button.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border: none;
                    padding: 5px 10px;
                    border-radius: 3px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
            """)
            
            # ربط الأزرار بالإجراءات
            document_id = document['id']
            view_button.clicked.connect(lambda checked, doc_id=document_id: self.view_document(doc_id))
            delete_button.clicked.connect(lambda checked, doc_id=document_id: self.delete_document(doc_id))
            
            actions_layout.addWidget(view_button)
            actions_layout.addWidget(delete_button)
            
            self.documents_table.setCellWidget(row, 5, actions_widget)
    
    def filter_documents(self):
        """تصفية المستندات حسب القسم"""
        department_id = self.department_filter.currentData()
        self.load_documents(department_id)
    
    def search_documents(self):
        """البحث في المستندات"""
        search_text = self.search_input.text()
        
        if not search_text:
            self.filter_documents()  # إعادة تحميل المستندات حسب القسم المحدد
            return
        
        result = self.document_manager.search_documents(search_text)
        
        if not result['success']:
            QMessageBox.warning(self, "خطأ", f"فشل في البحث عن المستندات: {result.get('error', '')}")
            return
        
        documents = result['documents']
        
        # تفريغ الجدول
        self.documents_table.setRowCount(0)
        
        # إضافة المستندات إلى الجدول (نفس الكود من load_documents)
        for row, document in enumerate(documents):
            self.documents_table.insertRow(row)
            
            # إضافة بيانات المستند
            id_item = QTableWidgetItem(str(document['id']))
            title_item = QTableWidgetItem(document['title'])
            
            # الحصول على اسم القسم
            department_name = "غير محدد"
            if document['department_id']:
                self.db.cursor.execute("SELECT name FROM departments WHERE id = ?", (document['department_id'],))
                department = self.db.cursor.fetchone()
                if department:
                    department_name = department['name']
            
            department_item = QTableWidgetItem(department_name)
            
            # تنسيق حجم الملف
            file_size = document['file_size']
            if file_size < 1024:
                size_str = f"{file_size} بايت"
            elif file_size < 1024 * 1024:
                size_str = f"{file_size / 1024:.2f} كيلوبايت"
            else:
                size_str = f"{file_size / (1024 * 1024):.2f} ميجابايت"
            
            size_item = QTableWidgetItem(size_str)
            
            # تنسيق التاريخ
            created_at = datetime.strptime(document['created_at'], "%Y-%m-%d %H:%M:%S")
            date_str = created_at.strftime("%Y/%m/%d %H:%M")
            date_item = QTableWidgetItem(date_str)
            
            self.documents_table.setItem(row, 0, id_item)
            self.documents_table.setItem(row, 1, title_item)
            self.documents_table.setItem(row, 2, department_item)
            self.documents_table.setItem(row, 3, size_item)
            self.documents_table.setItem(row, 4, date_item)
            
            # إضافة أزرار الإجراءات (نفس الكود من load_documents)
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(0, 0, 0, 0)
            
            view_button = QPushButton("عرض")
            view_button.setStyleSheet("""
                QPushButton {
                    background-color: #1e81b0;
                    color: white;
                    border: none;
                    padding: 5px 10px;
                    border-radius: 3px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #166d96;
                }
            """)
            
            delete_button = QPushButton("حذف")
            delete_button.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border: none;
                    padding: 5px 10px;
                    border-radius: 3px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
            """)
            
            # ربط الأزرار بالإجراءات
            document_id = document['id']
            view_button.clicked.connect(lambda checked, doc_id=document_id: self.view_document(doc_id))
            delete_button.clicked.connect(lambda checked, doc_id=document_id: self.delete_document(doc_id))
            
            actions_layout.addWidget(view_button)
            actions_layout.addWidget(delete_button)
            
            self.documents_table.setCellWidget(row, 5, actions_widget)
    
    def add_document(self):
        """إضافة مستند جديد"""
        if not self.current_user:
            QMessageBox.warning(self, "خطأ", "يجب تسجيل الدخول أولاً")
            return
        
        dialog = DocumentDialog(self, self.db)
        if dialog.exec_() == QDialog.Accepted:
            # إعادة تحميل المستندات
            self.filter_documents()
    
    def view_document(self, document_id):
        """عرض المستند"""
        result = self.document_manager.get_document_file_path(document_id)
        
        if not result['success']:
            QMessageBox.warning(self, "خطأ", f"فشل في فتح المستند: {result.get('error', '')}")
            return
        
        file_path = result['file_path']
        
        try:
            # فتح الملف باستخدام التطبيق الافتراضي
            if os.name == 'nt':  # Windows
                os.startfile(file_path)
            elif os.name == 'posix':  # macOS, Linux
                subprocess.call(('xdg-open', file_path))
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في فتح المستند: {str(e)}")
    
    def delete_document(self, document_id):
        """حذف المستند"""
        # تأكيد الحذف
        reply = QMessageBox.question(
            self, 
            "تأكيد الحذف", 
            "هل أنت متأكد من حذف هذا المستند؟ لا يمكن التراجع عن هذا الإجراء.",
            QMessageBox.Yes | QMessageBox.No, 
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            result = self.document_manager.delete_document(document_id)
            
            if result['success']:
                QMessageBox.information(self, "نجاح", "تم حذف المستند بنجاح")
                self.filter_documents()  # إعادة تحميل المستندات
            else:
                QMessageBox.warning(self, "خطأ", f"فشل في حذف المستند: {result.get('error', '')}")


class DocumentDialog(QDialog):
    """نافذة حوار إضافة مستند جديد"""
    
    def __init__(self, parent=None, db=None):
        super().__init__(parent)
        
        self.db = db
        self.document_manager = DocumentManager(db)
        
        # تعيين عنوان النافذة
        self.setWindowTitle("إضافة مستند جديد")
        self.setMinimumSize(500, 400)
        
        # إنشاء واجهة المستخدم
        self.init_ui()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # نموذج البيانات
        form_layout = QFormLayout()
        
        # حقل عنوان المستند
        self.title_input = QLineEdit()
        self.title_input.setPlaceholderText("أدخل عنوان المستند")
        self.title_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
            }
        """)
        
        # حقل وصف المستند
        self.description_input = QTextEdit()
        self.description_input.setPlaceholderText("أدخل وصف المستند (اختياري)")
        self.description_input.setStyleSheet("""
            QTextEdit {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
            }
        """)
        self.description_input.setMaximumHeight(100)
        
        # قائمة الأقسام
        self.department_combo = QComboBox()
        self.department_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
            }
        """)
        
        # إضافة الأقسام إلى القائمة
        departments = self.db.get_departments()
        self.department_combo.addItem("اختر القسم", None)
        for department in departments:
            self.department_combo.addItem(department['name'], department['id'])
        
        # حقل اختيار الملف
        file_layout = QHBoxLayout()
        
        self.file_path_input = QLineEdit()
        self.file_path_input.setPlaceholderText("اختر ملف المستند")
        self.file_path_input.setReadOnly(True)
        self.file_path_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
            }
        """)
        
        browse_button = QPushButton("استعراض...")
        browse_button.setStyleSheet("""
            QPushButton {
                background-color: #1e81b0;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #166d96;
            }
        """)
        browse_button.clicked.connect(self.browse_file)
        
        file_layout.addWidget(self.file_path_input)
        file_layout.addWidget(browse_button)
        
        # إضافة الحقول إلى النموذج
        form_layout.addRow("عنوان المستند:", self.title_input)
        form_layout.addRow("الوصف:", self.description_input)
        form_layout.addRow("القسم:", self.department_combo)
        form_layout.addRow("الملف:", file_layout)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setVisible(False)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        self.save_button = QPushButton("حفظ")
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #e91e63;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #d81b60;
            }
        """)
        self.save_button.clicked.connect(self.save_document)
        
        cancel_button = QPushButton("إلغاء")
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        cancel_button.clicked.connect(self.reject)
        
        buttons_layout.addWidget(self.save_button)
        buttons_layout.addWidget(cancel_button)
        
        # إضافة العناصر إلى التخطيط الرئيسي
        main_layout.addLayout(form_layout)
        main_layout.addWidget(self.progress_bar)
        main_layout.addStretch(1)
        main_layout.addLayout(buttons_layout)
    
    def browse_file(self):
        """اختيار ملف المستند"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "اختر ملف المستند",
            "",
            "جميع الملفات (*.*)"
        )
        
        if file_path:
            self.file_path_input.setText(file_path)
    
    def save_document(self):
        """حفظ المستند"""
        title = self.title_input.text()
        description = self.description_input.toPlainText()
        department_id = self.department_combo.currentData()
        file_path = self.file_path_input.text()
        
        if not title:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال عنوان المستند")
            return
        
        if not file_path:
            QMessageBox.warning(self, "خطأ", "يرجى اختيار ملف المستند")
            return
        
        # تعطيل الأزرار وإظهار شريط التقدم
        self.save_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(10)
        
        # إضافة المستند
        result = self.document_manager.add_document(
            file_path=file_path,
            title=title,
            description=description,
            department_id=department_id,
            user_id=1  # المستخدم الافتراضي
        )
        
        self.progress_bar.setValue(100)
        
        if result['success']:
            QMessageBox.information(self, "نجاح", "تم إضافة المستند بنجاح")
            self.accept()
        else:
            QMessageBox.warning(self, "خطأ", f"فشل في إضافة المستند: {result.get('error', '')}")
            self.save_button.setEnabled(True)
            self.progress_bar.setVisible(False)
