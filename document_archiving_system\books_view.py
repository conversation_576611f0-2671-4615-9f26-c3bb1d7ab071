#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
واجهة عرض الكتب
تتعامل مع عرض وإدارة الكتب
"""

import os
from datetime import datetime
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
                            QLineEdit, QTableWidget, QTableWidgetItem, QHeaderView,
                            QMessageBox, QDialog, QFormLayout, QFileDialog, QComboBox,
                            QDateEdit, QTextEdit, QFrame, QScrollArea, QGridLayout,
                            QListWidget, QListWidgetItem, QSplitter)
from PyQt5.QtGui import QIcon, QFont, QPixmap
from PyQt5.QtCore import Qt, QDate, QSize

from book_manager import BookManager


class BooksWidget(QWidget):
    """واجهة الكتب"""

    def __init__(self, db):
        super().__init__()

        # تهيئة قاعدة البيانات ومدير الكتب
        self.db = db
        self.book_manager = BookManager(db)

        # المستخدم الحالي
        self.current_user = None

        # إنشاء واجهة المستخدم
        self.init_ui()

        # تحميل الكتب
        self.load_books()

    def set_current_user(self, user):
        """تعيين المستخدم الحالي"""
        self.current_user = user

    def init_ui(self):
        """تهيئة واجهة المستخدم"""

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # عنوان الصفحة
        title_label = QLabel("إدارة الكتب")
        title_label.setStyleSheet("""
            font-size: 20px;
            font-weight: bold;
            color: #1a3a63;
        """)

        # شريط الأدوات
        toolbar_layout = QHBoxLayout()

        # زر إضافة كتاب جديد
        add_book_button = QPushButton("إضافة كتاب جديد")
        add_book_button.setStyleSheet("""
            QPushButton {
                background-color: #1a3a63;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2c5282;
            }
        """)
        add_book_button.clicked.connect(self.add_book)

        # حقل البحث
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("بحث في الكتب...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
            }
        """)

        search_button = QPushButton("بحث")
        search_button.setStyleSheet("""
            QPushButton {
                background-color: #16a085;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #138a72;
            }
        """)
        search_button.clicked.connect(self.search_books)

        # فلتر نوع الكتاب
        type_filter_label = QLabel("نوع الكتاب:")
        self.type_filter = QComboBox()
        self.type_filter.addItem("جميع الأنواع", None)
        self.load_book_types()
        self.type_filter.currentIndexChanged.connect(self.filter_books)

        # إضافة عناصر شريط الأدوات
        toolbar_layout.addWidget(add_book_button)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.search_input)
        toolbar_layout.addWidget(search_button)
        toolbar_layout.addWidget(type_filter_label)
        toolbar_layout.addWidget(self.type_filter)

        # جدول الكتب
        self.books_table = QTableWidget()
        self.books_table.setColumnCount(7)
        self.books_table.setHorizontalHeaderLabels([
            "رقم الكتاب", "نوع الكتاب", "التاريخ", "رقم القطعة", "اسم المقاطعة", "الملاحظات", "الإجراءات"
        ])
        self.books_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.books_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }
            QHeaderView::section {
                background-color: #1a3a63;
                color: white;
                padding: 8px;
                font-size: 14px;
                font-weight: bold;
                border: none;
            }
        """)

        # إضافة العناصر إلى التخطيط الرئيسي
        main_layout.addWidget(title_label)
        main_layout.addLayout(toolbar_layout)
        main_layout.addWidget(self.books_table)

    def load_book_types(self):
        """تحميل أنواع الكتب"""
        result = self.book_manager.get_book_types()
        if result['success']:
            for book_type in result['book_types']:
                self.type_filter.addItem(book_type['name'], book_type['id'])

    def load_books(self):
        """تحميل الكتب"""
        result = self.book_manager.get_books()

        if result['success']:
            self.display_books(result['books'])
        else:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل الكتب: {result.get('error', '')}")

    def display_books(self, books):
        """عرض الكتب في الجدول"""
        # تفريغ الجدول
        self.books_table.setRowCount(0)

        # إضافة الكتب إلى الجدول
        for row, book in enumerate(books):
            self.books_table.insertRow(row)

            # إضافة بيانات الكتاب
            book_number_item = QTableWidgetItem(book['book_number'])
            book_type_item = QTableWidgetItem(book['book_type_name'] or "غير محدد")
            date_item = QTableWidgetItem(book['date'])
            piece_number_item = QTableWidgetItem(book['piece_number'] or "")
            district_name_item = QTableWidgetItem(book['district_name'] or "")
            notes_item = QTableWidgetItem(book['notes'] or "")

            self.books_table.setItem(row, 0, book_number_item)
            self.books_table.setItem(row, 1, book_type_item)
            self.books_table.setItem(row, 2, date_item)
            self.books_table.setItem(row, 3, piece_number_item)
            self.books_table.setItem(row, 4, district_name_item)
            self.books_table.setItem(row, 5, notes_item)

            # إضافة أزرار الإجراءات
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(0, 0, 0, 0)

            view_button = QPushButton("عرض")
            view_button.setStyleSheet("""
                QPushButton {
                    background-color: #1e81b0;
                    color: white;
                    border: none;
                    padding: 5px 10px;
                    border-radius: 3px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #166d96;
                }
            """)

            edit_button = QPushButton("تعديل")
            edit_button.setStyleSheet("""
                QPushButton {
                    background-color: #16a085;
                    color: white;
                    border: none;
                    padding: 5px 10px;
                    border-radius: 3px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #138a72;
                }
            """)

            delete_button = QPushButton("حذف")
            delete_button.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border: none;
                    padding: 5px 10px;
                    border-radius: 3px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
            """)

            # ربط الأزرار بالإجراءات
            book_id = book['id']
            view_button.clicked.connect(lambda _, b_id=book_id: self.view_book(b_id))
            edit_button.clicked.connect(lambda _, b_id=book_id: self.edit_book(b_id))
            delete_button.clicked.connect(lambda _, b_id=book_id: self.delete_book(b_id))

            actions_layout.addWidget(view_button)
            actions_layout.addWidget(edit_button)
            actions_layout.addWidget(delete_button)

            self.books_table.setCellWidget(row, 6, actions_widget)

    def search_books(self):
        """البحث في الكتب"""
        search_text = self.search_input.text()

        if not search_text:
            self.load_books()
            return

        result = self.book_manager.search_books(search_text)

        if result['success']:
            self.display_books(result['books'])
        else:
            QMessageBox.warning(self, "خطأ", f"فشل في البحث عن الكتب: {result.get('error', '')}")

    def filter_books(self):
        """تصفية الكتب حسب النوع"""
        book_type_id = self.type_filter.currentData()

        result = self.book_manager.get_books(book_type_id=book_type_id)

        if result['success']:
            self.display_books(result['books'])
        else:
            QMessageBox.warning(self, "خطأ", f"فشل في تصفية الكتب: {result.get('error', '')}")

    def add_book(self):
        """إضافة كتاب جديد"""
        if not self.current_user:
            QMessageBox.warning(self, "خطأ", "يجب تسجيل الدخول أولاً")
            return

        dialog = AddBookDialog(self, self.db, self.book_manager)
        if dialog.exec() == QDialog.Accepted:
            self.load_books()

    def view_book(self, book_id):
        """عرض تفاصيل الكتاب"""
        dialog = ViewBookDialog(self, book_id, self.book_manager)
        dialog.exec()

    def edit_book(self, book_id):
        """تعديل الكتاب"""
        dialog = EditBookDialog(self, book_id, self.db, self.book_manager)
        if dialog.exec() == QDialog.Accepted:
            self.load_books()

    def delete_book(self, book_id):
        """حذف الكتاب"""
        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            "هل أنت متأكد من حذف هذا الكتاب؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            result = self.book_manager.delete_book(book_id)

            if result['success']:
                QMessageBox.information(self, "نجاح", "تم حذف الكتاب بنجاح")
                self.load_books()
            else:
                QMessageBox.warning(self, "خطأ", f"فشل في حذف الكتاب: {result.get('error', '')}")


class AddBookDialog(QDialog):
    """نافذة إضافة كتاب جديد"""

    def __init__(self, parent, db, book_manager):
        super().__init__(parent)

        self.db = db
        self.book_manager = book_manager
        self.selected_images = []

        # تعيين عنوان النافذة
        self.setWindowTitle("إضافة كتاب جديد - نظام أرشفة الكتب")
        self.setMinimumSize(1100, 700)
        self.resize(1200, 750)

        # تعيين النافذة في وسط الشاشة
        self.center_window()

        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', 'Arial', sans-serif;
            }
        """)

        # إنشاء واجهة المستخدم
        self.init_ui()

    def center_window(self):
        """توسيط النافذة في الشاشة"""
        from PyQt5.QtWidgets import QApplication
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )

    def init_ui(self):
        """تهيئة واجهة المستخدم"""

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(10)

        # شريط العنوان المصغر
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1a3a63, stop:1 #2c5282);
                border-radius: 6px;
                padding: 8px 15px;
                margin-bottom: 8px;
            }
        """)

        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(5, 5, 5, 5)

        # أيقونة مصغرة
        icon_label = QLabel("📚")
        icon_label.setStyleSheet("""
            font-size: 20px;
            color: white;
            margin-right: 8px;
        """)

        # عنوان مصغر
        title_label = QLabel("إضافة كتاب جديد")
        title_label.setStyleSheet("""
            font-size: 14px;
            font-weight: bold;
            color: white;
            margin: 0;
        """)

        header_layout.addWidget(icon_label)
        header_layout.addWidget(title_label)
        header_layout.addStretch()

        # المحتوى الرئيسي - تخطيط أفقي
        content_widget = QWidget()
        content_layout = QHBoxLayout(content_widget)
        content_layout.setSpacing(15)

        # القسم الأيمن - نموذج البيانات
        right_panel = QWidget()
        right_panel.setMinimumWidth(550)
        right_panel.setMaximumWidth(600)
        right_panel_layout = QVBoxLayout(right_panel)
        right_panel_layout.setSpacing(10)

        # عنوان القسم الأيمن المصغر
        right_title = QLabel("📝 بيانات الكتاب")
        right_title.setStyleSheet("""
            font-size: 14px;
            font-weight: bold;
            color: #1a3a63;
            padding: 6px 10px;
            background-color: rgba(26, 58, 99, 0.1);
            border-radius: 6px;
            margin-bottom: 8px;
        """)
        right_title.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # نموذج البيانات
        form_frame = QFrame()
        form_frame.setFrameShape(QFrame.StyledPanel)
        form_frame.setStyleSheet("""
            QFrame {
                border: 2px solid #e9ecef;
                border-radius: 8px;
                background-color: white;
                padding: 20px;
                margin: 3px;
            }
            QFrame:hover {
                border: 2px solid #1a3a63;
            }
        """)

        form_layout = QGridLayout(form_frame)
        form_layout.setSpacing(12)
        form_layout.setColumnStretch(1, 1)

        # نوع الكتاب
        self.book_type_combo = QComboBox()
        self.book_type_combo.setStyleSheet("""
            QComboBox {
                padding: 12px;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                font-size: 14px;
                background-color: #f8f9fa;
                min-height: 20px;
            }
            QComboBox:focus {
                border: 2px solid #1a3a63;
                background-color: white;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border: 5px solid transparent;
                border-top: 8px solid #1a3a63;
                margin-right: 10px;
            }
        """)
        self.load_book_types()
        self.book_type_combo.currentIndexChanged.connect(self.book_type_changed)

        # رقم الكتاب
        self.book_number_input = QLineEdit()
        self.book_number_input.setPlaceholderText("أدخل رقم الكتاب...")
        self.book_number_input.setStyleSheet("""
            QLineEdit {
                padding: 12px;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                font-size: 14px;
                background-color: #f8f9fa;
                min-height: 20px;
            }
            QLineEdit:focus {
                border: 2px solid #1a3a63;
                background-color: white;
            }
            QLineEdit:hover {
                border: 2px solid #2c5282;
            }
        """)

        # التاريخ
        self.date_input = QDateEdit()
        self.date_input.setCalendarPopup(True)
        self.date_input.setDate(QDate.currentDate())
        self.date_input.setStyleSheet("""
            QDateEdit {
                padding: 12px;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                font-size: 14px;
                background-color: #f8f9fa;
                min-height: 20px;
            }
            QDateEdit:focus {
                border: 2px solid #1a3a63;
                background-color: white;
            }
            QDateEdit::drop-down {
                border: none;
                width: 30px;
            }
        """)

        # رقم القطعة (للتعارض خدمات)
        self.piece_number_input = QLineEdit()
        self.piece_number_input.setPlaceholderText("أدخل رقم القطعة...")
        self.piece_number_input.setStyleSheet("""
            QLineEdit {
                padding: 15px;
                border: 2px solid #e9ecef;
                border-radius: 10px;
                font-size: 16px;
                background-color: #f8f9fa;
                min-height: 25px;
            }
            QLineEdit:focus {
                border: 2px solid #1a3a63;
                background-color: white;
            }
            QLineEdit:hover {
                border: 2px solid #2c5282;
            }
        """)
        self.piece_number_label = QLabel("رقم القطعة:")
        self.piece_number_label.setStyleSheet("font-weight: bold; color: #1a3a63; font-size: 16px; margin-bottom: 8px;")

        # اسم المقاطعة (للتعارض خدمات)
        self.district_name_input = QLineEdit()
        self.district_name_input.setPlaceholderText("أدخل اسم المقاطعة...")
        self.district_name_input.setStyleSheet("""
            QLineEdit {
                padding: 15px;
                border: 2px solid #e9ecef;
                border-radius: 10px;
                font-size: 16px;
                background-color: #f8f9fa;
                min-height: 25px;
            }
            QLineEdit:focus {
                border: 2px solid #1a3a63;
                background-color: white;
            }
            QLineEdit:hover {
                border: 2px solid #2c5282;
            }
        """)
        self.district_name_label = QLabel("اسم المقاطعة:")
        self.district_name_label.setStyleSheet("font-weight: bold; color: #1a3a63; font-size: 16px; margin-bottom: 8px;")

        # الملاحظات
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(100)
        self.notes_input.setMinimumHeight(80)
        self.notes_input.setPlaceholderText("أدخل الملاحظات...")
        self.notes_input.setStyleSheet("""
            QTextEdit {
                padding: 15px;
                border: 2px solid #e9ecef;
                border-radius: 10px;
                font-size: 16px;
                background-color: #f8f9fa;
            }
            QTextEdit:focus {
                border: 2px solid #1a3a63;
                background-color: white;
            }
        """)

        # القسم
        self.department_combo = QComboBox()
        self.department_combo.addItem("اختر القسم", None)
        self.department_combo.setStyleSheet("""
            QComboBox {
                padding: 15px;
                border: 2px solid #e9ecef;
                border-radius: 10px;
                font-size: 16px;
                background-color: #f8f9fa;
                min-height: 25px;
            }
            QComboBox:focus {
                border: 2px solid #1a3a63;
                background-color: white;
            }
            QComboBox::drop-down {
                border: none;
                width: 35px;
            }
            QComboBox::down-arrow {
                image: none;
                border: 6px solid transparent;
                border-top: 10px solid #1a3a63;
                margin-right: 12px;
            }
        """)
        self.load_departments()

        # تنسيق التسميات
        label_style = "font-weight: bold; color: #1a3a63; font-size: 14px; margin-bottom: 8px;"

        # إضافة الحقول إلى النموذج باستخدام Grid Layout
        row = 0

        # نوع الكتاب
        book_type_label = QLabel("نوع الكتاب:")
        book_type_label.setStyleSheet(label_style)
        form_layout.addWidget(book_type_label, row, 0)
        form_layout.addWidget(self.book_type_combo, row, 1)
        row += 1

        # رقم الكتاب
        book_number_label = QLabel("رقم الكتاب:")
        book_number_label.setStyleSheet(label_style)
        form_layout.addWidget(book_number_label, row, 0)
        form_layout.addWidget(self.book_number_input, row, 1)
        row += 1

        # التاريخ
        date_label = QLabel("التاريخ:")
        date_label.setStyleSheet(label_style)
        form_layout.addWidget(date_label, row, 0)
        form_layout.addWidget(self.date_input, row, 1)
        row += 1

        # حقول تعارض الخدمات
        form_layout.addWidget(self.piece_number_label, row, 0)
        form_layout.addWidget(self.piece_number_input, row, 1)
        row += 1

        form_layout.addWidget(self.district_name_label, row, 0)
        form_layout.addWidget(self.district_name_input, row, 1)
        row += 1

        # القسم
        department_label = QLabel("القسم:")
        department_label.setStyleSheet(label_style)
        form_layout.addWidget(department_label, row, 0)
        form_layout.addWidget(self.department_combo, row, 1)
        row += 1

        # الملاحظات
        notes_label = QLabel("الملاحظات:")
        notes_label.setStyleSheet(label_style)
        form_layout.addWidget(notes_label, row, 0, Qt.AlignmentFlag.AlignTop)
        form_layout.addWidget(self.notes_input, row, 1)

        # إخفاء حقول تعارض الخدمات افتراضيًا
        self.piece_number_label.hide()
        self.piece_number_input.hide()
        self.district_name_label.hide()
        self.district_name_input.hide()

        # إضافة النموذج إلى القسم الأيمن
        right_panel_layout.addWidget(right_title)
        right_panel_layout.addWidget(form_frame)
        right_panel_layout.addStretch()

        # القسم الأيسر - إدارة الصور
        left_panel = QWidget()
        left_panel.setMinimumWidth(450)
        left_panel.setMaximumWidth(500)
        left_panel_layout = QVBoxLayout(left_panel)
        left_panel_layout.setSpacing(10)

        # عنوان القسم الأيسر المصغر
        left_title = QLabel("📷 إدارة الصور")
        left_title.setStyleSheet("""
            font-size: 14px;
            font-weight: bold;
            color: #16a085;
            padding: 6px 10px;
            background-color: rgba(22, 160, 133, 0.1);
            border-radius: 6px;
            margin-bottom: 8px;
        """)
        left_title.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # قسم الصور
        images_frame = QFrame()
        images_frame.setFrameShape(QFrame.StyledPanel)
        images_frame.setStyleSheet("""
            QFrame {
                border: 2px solid #e9ecef;
                border-radius: 8px;
                background-color: white;
                padding: 20px;
                margin: 3px;
            }
            QFrame:hover {
                border: 2px solid #16a085;
            }
        """)

        images_layout = QVBoxLayout(images_frame)
        images_layout.setSpacing(10)

        # معلومات الصور المصغرة
        info_label = QLabel("إضافة صور من الملفات أو السكانر")
        info_label.setStyleSheet("""
            font-size: 12px;
            color: #666;
            padding: 6px 8px;
            background-color: rgba(22, 160, 133, 0.1);
            border-radius: 6px;
            margin-bottom: 10px;
        """)
        info_label.setWordWrap(True)
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # أزرار إدارة الصور
        images_buttons_layout = QHBoxLayout()

        add_images_button = QPushButton("📁 إضافة ملفات")
        add_images_button.setStyleSheet("""
            QPushButton {
                background-color: #16a085;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 6px;
                font-size: 13px;
                font-weight: bold;
                min-height: 32px;
            }
            QPushButton:hover {
                background-color: #138d75;
            }
        """)
        add_images_button.clicked.connect(self.add_images)

        scan_images_button = QPushButton("📷 سكانر")
        scan_images_button.setStyleSheet("""
            QPushButton {
                background-color: #e91e63;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 6px;
                font-size: 13px;
                font-weight: bold;
                min-height: 32px;
            }
            QPushButton:hover {
                background-color: #d81b60;
            }
        """)
        scan_images_button.clicked.connect(self.scan_images)

        images_buttons_layout.addWidget(add_images_button)
        images_buttons_layout.addWidget(scan_images_button)
        images_buttons_layout.addStretch()

        # قائمة الصور المحسنة
        self.images_list = QListWidget()
        self.images_list.setMinimumHeight(320)
        self.images_list.setStyleSheet("""
            QListWidget {
                border: 2px solid #e9ecef;
                border-radius: 10px;
                background-color: #f8f9fa;
                padding: 15px;
                font-size: 14px;
            }
            QListWidget::item {
                padding: 12px;
                border-radius: 8px;
                margin: 3px;
                background-color: white;
                border: 1px solid #ddd;
            }
            QListWidget::item:selected {
                background-color: #1a3a63;
                color: white;
                border: 1px solid #1a3a63;
            }
            QListWidget::item:hover {
                background-color: #e9ecef;
                border: 1px solid #16a085;
            }
        """)

        # عداد الصور المصغر
        self.images_count_label = QLabel("عدد الصور: 0")
        self.images_count_label.setStyleSheet("""
            font-size: 12px;
            color: #666;
            font-weight: bold;
            padding: 4px 8px;
            background-color: rgba(22, 160, 133, 0.1);
            border-radius: 4px;
            margin: 3px 0;
        """)
        self.images_count_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        images_layout.addWidget(info_label)
        images_layout.addLayout(images_buttons_layout)
        images_layout.addWidget(self.images_count_label)
        images_layout.addWidget(self.images_list)

        # إضافة قسم الصور إلى القسم الأيسر
        left_panel_layout.addWidget(left_title)
        left_panel_layout.addWidget(images_frame)
        left_panel_layout.addStretch()

        # إضافة الأقسام إلى التخطيط الرئيسي
        content_layout.addWidget(right_panel)  # القسم الأيمن للإدخالات
        content_layout.addWidget(left_panel)   # القسم الأيسر للصور

        # أزرار الحفظ والإلغاء
        buttons_layout = QHBoxLayout()

        save_button = QPushButton("💾 حفظ")
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 20px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
                min-height: 32px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        save_button.clicked.connect(self.save_book)

        cancel_button = QPushButton("❌ إلغاء")
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 8px 20px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
                min-height: 32px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        cancel_button.clicked.connect(self.reject)

        buttons_layout.addStretch()
        buttons_layout.addWidget(save_button)
        buttons_layout.addWidget(cancel_button)

        # إضافة العناصر إلى التخطيط الرئيسي
        main_layout.addWidget(header_frame)
        main_layout.addWidget(content_widget)
        main_layout.addLayout(buttons_layout)

    def load_book_types(self):
        """تحميل أنواع الكتب"""
        result = self.book_manager.get_book_types()
        if result['success']:
            for book_type in result['book_types']:
                self.book_type_combo.addItem(book_type['name'], book_type['id'])

    def load_departments(self):
        """تحميل الأقسام"""
        departments = self.db.get_departments()
        for department in departments:
            self.department_combo.addItem(department['name'], department['id'])

    def book_type_changed(self):
        """تغيير نوع الكتاب"""
        current_text = self.book_type_combo.currentText()

        if current_text == "تعارض خدمات":
            # إظهار حقول تعارض الخدمات
            self.piece_number_label.show()
            self.piece_number_input.show()
            self.district_name_label.show()
            self.district_name_input.show()
        else:
            # إخفاء حقول تعارض الخدمات
            self.piece_number_label.hide()
            self.piece_number_input.hide()
            self.district_name_label.hide()
            self.district_name_input.hide()

    def add_images(self):
        """إضافة صور من الملفات"""
        file_paths, _ = QFileDialog.getOpenFileNames(
            self,
            "اختر الصور",
            "",
            "ملفات الصور (*.png *.jpg *.jpeg *.bmp *.gif *.tiff)"
        )

        for file_path in file_paths:
            if file_path not in self.selected_images:
                self.selected_images.append(file_path)

                # إضافة الصورة إلى القائمة
                file_name = os.path.basename(file_path)
                file_size = os.path.getsize(file_path)
                size_str = self.format_file_size(file_size)

                item = QListWidgetItem(f"📄 {file_name} ({size_str})")
                item.setData(Qt.UserRole, file_path)
                item.setToolTip(f"المسار: {file_path}\nالحجم: {size_str}")
                self.images_list.addItem(item)

        # تحديث عداد الصور
        self.update_images_count()

    def format_file_size(self, size_bytes):
        """تنسيق حجم الملف"""
        if size_bytes < 1024:
            return f"{size_bytes} بايت"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} كيلوبايت"
        else:
            return f"{size_bytes / (1024 * 1024):.1f} ميجابايت"

    def update_images_count(self):
        """تحديث عداد الصور"""
        count = len(self.selected_images)
        self.images_count_label.setText(f"عدد الصور: {count}")

        if count > 0:
            self.images_count_label.setStyleSheet("""
                font-size: 14px;
                color: #16a085;
                font-weight: bold;
                padding: 5px;
            """)
        else:
            self.images_count_label.setStyleSheet("""
                font-size: 14px;
                color: #666;
                font-weight: bold;
                padding: 5px;
            """)

    def scan_images(self):
        """مسح الصور من السكانر"""
        try:
            # محاولة استيراد مكتبة السكانر
            import subprocess
            import tempfile
            import os
            from datetime import datetime

            # إنشاء نافذة حوار للسكانر
            scanner_dialog = ScannerDialog(self)
            if scanner_dialog.exec_() == QDialog.Accepted:
                scanned_images = scanner_dialog.get_scanned_images()

                # إضافة الصور الممسوحة إلى القائمة
                for image_path in scanned_images:
                    if image_path not in self.selected_images:
                        self.selected_images.append(image_path)

                        file_name = os.path.basename(image_path)
                        file_size = os.path.getsize(image_path)
                        size_str = self.format_file_size(file_size)

                        item = QListWidgetItem(f"📷 {file_name} ({size_str})")
                        item.setData(Qt.UserRole, image_path)
                        item.setToolTip(f"المسار: {image_path}\nالحجم: {size_str}")
                        self.images_list.addItem(item)

                # تحديث عداد الصور
                self.update_images_count()

        except ImportError:
            # في حالة عدم توفر مكتبة السكانر، استخدم محاكي
            self.simulate_scanner()

    def simulate_scanner(self):
        """محاكي السكانر للاختبار"""
        reply = QMessageBox.question(
            self,
            "محاكي السكانر",
            "هل تريد محاكاة مسح صورة من السكانر؟\n(سيتم إنشاء صورة تجريبية)",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )

        if reply == QMessageBox.Yes:
            try:
                # إنشاء صورة تجريبية
                from PIL import Image, ImageDraw, ImageFont
                import tempfile
                import os
                from datetime import datetime

                # إنشاء صورة تجريبية
                img = Image.new('RGB', (800, 600), color='white')
                draw = ImageDraw.Draw(img)

                # إضافة نص تجريبي
                try:
                    font = ImageFont.truetype("arial.ttf", 40)
                except:
                    font = ImageFont.load_default()

                text = f"صورة ممسوحة\n{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                draw.text((50, 250), text, fill='black', font=font)

                # حفظ الصورة في مجلد مؤقت
                temp_dir = tempfile.gettempdir()
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                image_path = os.path.join(temp_dir, f"scanned_image_{timestamp}.png")
                img.save(image_path)

                # إضافة الصورة إلى القائمة
                if image_path not in self.selected_images:
                    self.selected_images.append(image_path)

                    file_name = os.path.basename(image_path)
                    file_size = os.path.getsize(image_path)
                    size_str = self.format_file_size(file_size)

                    item = QListWidgetItem(f"📷 {file_name} ({size_str})")
                    item.setData(Qt.UserRole, image_path)
                    item.setToolTip(f"المسار: {image_path}\nالحجم: {size_str}")
                    self.images_list.addItem(item)

                # تحديث عداد الصور
                self.update_images_count()

                QMessageBox.information(
                    self,
                    "نجح المسح",
                    f"تم إنشاء صورة تجريبية بنجاح!\nالمسار: {image_path}"
                )

            except ImportError:
                QMessageBox.warning(
                    self,
                    "خطأ",
                    "مكتبة PIL غير متوفرة.\nيرجى تثبيتها باستخدام: pip install Pillow"
                )
            except Exception as e:
                QMessageBox.warning(
                    self,
                    "خطأ",
                    f"فشل في إنشاء الصورة التجريبية:\n{str(e)}"
                )

    def save_book(self):
        """حفظ الكتاب"""
        # التحقق من صحة البيانات
        if not self.book_number_input.text():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال رقم الكتاب")
            return

        if self.book_type_combo.currentIndex() == -1:
            QMessageBox.warning(self, "خطأ", "يرجى اختيار نوع الكتاب")
            return

        # التحقق من حقول تعارض الخدمات
        current_text = self.book_type_combo.currentText()
        if current_text == "تعارض خدمات":
            if not self.piece_number_input.text():
                QMessageBox.warning(self, "خطأ", "يرجى إدخال رقم القطعة")
                return
            if not self.district_name_input.text():
                QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المقاطعة")
                return

        # إعداد بيانات الكتاب
        book_data = {
            'book_number': self.book_number_input.text(),
            'book_type_id': self.book_type_combo.currentData(),
            'date': self.date_input.date().toString("yyyy-MM-dd"),
            'notes': self.notes_input.toPlainText(),
            'piece_number': self.piece_number_input.text(),
            'district_name': self.district_name_input.text(),
            'department_id': self.department_combo.currentData(),
            'created_by': getattr(self.parent().current_user, 'id', None) if hasattr(self.parent(), 'current_user') else None
        }

        # حفظ الكتاب
        result = self.book_manager.add_book(book_data, self.selected_images)

        if result['success']:
            QMessageBox.information(self, "نجاح", "تم إضافة الكتاب بنجاح")
            self.accept()
        else:
            QMessageBox.warning(self, "خطأ", f"فشل في إضافة الكتاب: {result.get('error', '')}")


class ViewBookDialog(QDialog):
    """نافذة عرض تفاصيل الكتاب"""

    def __init__(self, parent, book_id, book_manager):
        super().__init__(parent)

        self.book_id = book_id
        self.book_manager = book_manager

        # تعيين عنوان النافذة
        self.setWindowTitle("تفاصيل الكتاب")
        self.setMinimumSize(700, 600)

        # إنشاء واجهة المستخدم
        self.init_ui()

        # تحميل بيانات الكتاب
        self.load_book_data()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # عنوان النافذة
        self.title_label = QLabel("تفاصيل الكتاب")
        self.title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #1a3a63;
        """)
        self.title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # إنشاء سبليتر للتقسيم
        splitter = QSplitter(Qt.Horizontal)

        # الجانب الأيسر - تفاصيل الكتاب
        details_frame = QFrame()
        details_frame.setFrameShape(QFrame.StyledPanel)
        details_frame.setStyleSheet("""
            QFrame {
                border: 1px solid #ddd;
                border-radius: 8px;
                background-color: white;
                padding: 15px;
            }
        """)

        details_layout = QFormLayout(details_frame)

        # إنشاء تسميات لعرض البيانات
        self.book_number_label = QLabel()
        self.book_type_label = QLabel()
        self.date_label = QLabel()
        self.piece_number_label = QLabel()
        self.district_name_label = QLabel()
        self.notes_label = QLabel()
        self.department_label = QLabel()
        self.created_by_label = QLabel()
        self.created_at_label = QLabel()

        # تنسيق التسميات
        for label in [self.book_number_label, self.book_type_label, self.date_label,
                     self.piece_number_label, self.district_name_label, self.notes_label,
                     self.department_label, self.created_by_label, self.created_at_label]:
            label.setStyleSheet("""
                QLabel {
                    padding: 8px;
                    border: 1px solid #eee;
                    border-radius: 4px;
                    background-color: #f9f9f9;
                    font-size: 14px;
                }
            """)
            label.setWordWrap(True)

        # إضافة الحقول إلى النموذج
        details_layout.addRow("رقم الكتاب:", self.book_number_label)
        details_layout.addRow("نوع الكتاب:", self.book_type_label)
        details_layout.addRow("التاريخ:", self.date_label)
        details_layout.addRow("رقم القطعة:", self.piece_number_label)
        details_layout.addRow("اسم المقاطعة:", self.district_name_label)
        details_layout.addRow("الملاحظات:", self.notes_label)
        details_layout.addRow("القسم:", self.department_label)
        details_layout.addRow("تمت الإضافة بواسطة:", self.created_by_label)
        details_layout.addRow("تاريخ الإضافة:", self.created_at_label)

        # الجانب الأيمن - الصور
        images_frame = QFrame()
        images_frame.setFrameShape(QFrame.StyledPanel)
        images_frame.setStyleSheet("""
            QFrame {
                border: 1px solid #ddd;
                border-radius: 8px;
                background-color: white;
                padding: 15px;
            }
        """)

        images_layout = QVBoxLayout(images_frame)

        images_title = QLabel("صور الكتاب")
        images_title.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #1a3a63;
        """)

        # منطقة عرض الصور
        self.images_scroll = QScrollArea()
        self.images_widget = QWidget()
        self.images_grid = QGridLayout(self.images_widget)
        self.images_scroll.setWidget(self.images_widget)
        self.images_scroll.setWidgetResizable(True)

        images_layout.addWidget(images_title)
        images_layout.addWidget(self.images_scroll)

        # إضافة الإطارات إلى السبليتر
        splitter.addWidget(details_frame)
        splitter.addWidget(images_frame)
        splitter.setSizes([400, 300])

        # زر الإغلاق
        close_button = QPushButton("إغلاق")
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        close_button.clicked.connect(self.accept)

        # إضافة العناصر إلى التخطيط الرئيسي
        main_layout.addWidget(self.title_label)
        main_layout.addWidget(splitter)

        close_layout = QHBoxLayout()
        close_layout.addStretch()
        close_layout.addWidget(close_button)
        main_layout.addLayout(close_layout)

    def load_book_data(self):
        """تحميل بيانات الكتاب"""
        result = self.book_manager.get_book_by_id(self.book_id)

        if result['success']:
            book = result['book']

            # تحديث عنوان النافذة
            self.title_label.setText(f"تفاصيل الكتاب: {book['book_number']}")

            # عرض البيانات
            self.book_number_label.setText(book['book_number'])
            self.book_type_label.setText(book['book_type_name'] or "غير محدد")
            self.date_label.setText(book['date'])
            self.piece_number_label.setText(book['piece_number'] or "غير محدد")
            self.district_name_label.setText(book['district_name'] or "غير محدد")
            self.notes_label.setText(book['notes'] or "لا توجد ملاحظات")
            self.department_label.setText(book['department_name'] or "غير محدد")
            self.created_by_label.setText(book['created_by_name'] or "غير محدد")
            self.created_at_label.setText(book['created_at'])

            # تحميل الصور
            self.load_images()
        else:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل بيانات الكتاب: {result.get('error', '')}")

    def load_images(self):
        """تحميل صور الكتاب"""
        result = self.book_manager.get_book_images(self.book_id)

        if result['success']:
            images = result['images']

            # تفريغ الشبكة
            for i in reversed(range(self.images_grid.count())):
                self.images_grid.itemAt(i).widget().setParent(None)

            # عرض الصور
            row = 0
            col = 0
            for image in images:
                if os.path.exists(image['image_path']):
                    # إنشاء تسمية للصورة
                    image_label = QLabel()
                    pixmap = QPixmap(image['image_path'])

                    # تغيير حجم الصورة
                    scaled_pixmap = pixmap.scaled(150, 150, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                    image_label.setPixmap(scaled_pixmap)
                    image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                    image_label.setStyleSheet("""
                        QLabel {
                            border: 1px solid #ddd;
                            border-radius: 4px;
                            padding: 5px;
                            background-color: white;
                        }
                    """)

                    # إضافة الصورة إلى الشبكة
                    self.images_grid.addWidget(image_label, row, col)

                    col += 1
                    if col >= 2:  # عمودين في كل صف
                        col = 0
                        row += 1

            if not images:
                no_images_label = QLabel("لا توجد صور لهذا الكتاب")
                no_images_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                no_images_label.setStyleSheet("color: #999; font-size: 14px;")
                self.images_grid.addWidget(no_images_label, 0, 0)


class EditBookDialog(AddBookDialog):
    """نافذة تعديل الكتاب"""

    def __init__(self, parent, book_id, db, book_manager):
        self.book_id = book_id
        super().__init__(parent, db, book_manager)

        # تغيير عنوان النافذة
        self.setWindowTitle("تعديل الكتاب")

        # تحميل بيانات الكتاب
        self.load_book_data()

    def load_book_data(self):
        """تحميل بيانات الكتاب للتعديل"""
        result = self.book_manager.get_book_by_id(self.book_id)

        if result['success']:
            book = result['book']

            # ملء الحقول بالبيانات الحالية
            self.book_number_input.setText(book['book_number'])

            # تعيين نوع الكتاب
            for i in range(self.book_type_combo.count()):
                if self.book_type_combo.itemData(i) == book['book_type_id']:
                    self.book_type_combo.setCurrentIndex(i)
                    break

            # تعيين التاريخ
            date = QDate.fromString(book['date'], "yyyy-MM-dd")
            self.date_input.setDate(date)

            # ملء الحقول الأخرى
            self.piece_number_input.setText(book['piece_number'] or "")
            self.district_name_input.setText(book['district_name'] or "")
            self.notes_input.setPlainText(book['notes'] or "")

            # تعيين القسم
            for i in range(self.department_combo.count()):
                if self.department_combo.itemData(i) == book['department_id']:
                    self.department_combo.setCurrentIndex(i)
                    break
        else:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل بيانات الكتاب: {result.get('error', '')}")

    def save_book(self):
        """حفظ تعديلات الكتاب"""
        # التحقق من صحة البيانات
        if not self.book_number_input.text():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال رقم الكتاب")
            return

        # التحقق من حقول تعارض الخدمات
        current_text = self.book_type_combo.currentText()
        if current_text == "تعارض خدمات":
            if not self.piece_number_input.text():
                QMessageBox.warning(self, "خطأ", "يرجى إدخال رقم القطعة")
                return
            if not self.district_name_input.text():
                QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المقاطعة")
                return

        # إعداد بيانات الكتاب
        book_data = {
            'book_number': self.book_number_input.text(),
            'date': self.date_input.date().toString("yyyy-MM-dd"),
            'notes': self.notes_input.toPlainText(),
            'piece_number': self.piece_number_input.text(),
            'district_name': self.district_name_input.text(),
            'department_id': self.department_combo.currentData()
        }

        # تحديث الكتاب
        result = self.book_manager.update_book(self.book_id, book_data)

        if result['success']:
            # إضافة الصور الجديدة إذا كانت موجودة
            if self.selected_images:
                for image_path in self.selected_images:
                    self.book_manager.add_book_image(self.book_id, image_path)

            QMessageBox.information(self, "نجاح", "تم تحديث الكتاب بنجاح")
            self.accept()
        else:
            QMessageBox.warning(self, "خطأ", f"فشل في تحديث الكتاب: {result.get('error', '')}")


class ScannerDialog(QDialog):
    """نافذة حوار السكانر"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.scanned_images = []
        self.init_ui()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("مسح الصور من السكانر")
        self.setFixedSize(400, 300)

        layout = QVBoxLayout(self)

        # عنوان
        title = QLabel("📷 مسح الصور من السكانر")
        title.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #1a3a63;
            padding: 10px;
            text-align: center;
        """)
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # معلومات
        info = QLabel("اختر إعدادات المسح:")
        info.setStyleSheet("font-size: 14px; margin: 10px;")

        # إعدادات المسح
        settings_frame = QFrame()
        settings_frame.setStyleSheet("""
            QFrame {
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 15px;
                background-color: #f9f9f9;
            }
        """)

        settings_layout = QVBoxLayout(settings_frame)

        # جودة المسح
        quality_layout = QHBoxLayout()
        quality_label = QLabel("جودة المسح:")
        self.quality_combo = QComboBox()
        self.quality_combo.addItems(["150 DPI", "300 DPI", "600 DPI"])
        self.quality_combo.setCurrentIndex(1)  # 300 DPI افتراضي

        quality_layout.addWidget(quality_label)
        quality_layout.addWidget(self.quality_combo)

        # نوع المسح
        type_layout = QHBoxLayout()
        type_label = QLabel("نوع المسح:")
        self.type_combo = QComboBox()
        self.type_combo.addItems(["ملون", "رمادي", "أبيض وأسود"])

        type_layout.addWidget(type_label)
        type_layout.addWidget(self.type_combo)

        settings_layout.addLayout(quality_layout)
        settings_layout.addLayout(type_layout)

        # أزرار
        buttons_layout = QHBoxLayout()

        scan_button = QPushButton("📷 بدء المسح")
        scan_button.setStyleSheet("""
            QPushButton {
                background-color: #16a085;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138d75;
            }
        """)
        scan_button.clicked.connect(self.start_scan)

        cancel_button = QPushButton("إلغاء")
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        cancel_button.clicked.connect(self.reject)

        buttons_layout.addStretch()
        buttons_layout.addWidget(scan_button)
        buttons_layout.addWidget(cancel_button)

        # إضافة العناصر
        layout.addWidget(title)
        layout.addWidget(info)
        layout.addWidget(settings_frame)
        layout.addLayout(buttons_layout)

    def start_scan(self):
        """بدء عملية المسح"""
        # محاكاة عملية المسح
        progress = QProgressDialog("جاري المسح...", "إلغاء", 0, 100, self)
        progress.setWindowModality(Qt.WindowModal)
        progress.show()

        # محاكاة تقدم المسح
        for i in range(101):
            progress.setValue(i)
            QApplication.processEvents()
            if progress.wasCanceled():
                return

            # تأخير صغير لمحاكاة المسح
            import time
            time.sleep(0.02)

        progress.close()

        # إنشاء صورة تجريبية
        try:
            from PIL import Image, ImageDraw, ImageFont
            import tempfile
            import os
            from datetime import datetime

            # إنشاء صورة تجريبية
            img = Image.new('RGB', (800, 600), color='white')
            draw = ImageDraw.Draw(img)

            # إضافة نص تجريبي
            try:
                font = ImageFont.truetype("arial.ttf", 30)
            except:
                font = ImageFont.load_default()

            quality = self.quality_combo.currentText()
            scan_type = self.type_combo.currentText()
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            text = f"صورة ممسوحة\nالجودة: {quality}\nالنوع: {scan_type}\nالوقت: {timestamp}"
            draw.text((50, 200), text, fill='black', font=font)

            # حفظ الصورة
            temp_dir = tempfile.gettempdir()
            timestamp_file = datetime.now().strftime("%Y%m%d_%H%M%S")
            image_path = os.path.join(temp_dir, f"scanned_{timestamp_file}.png")
            img.save(image_path)

            self.scanned_images.append(image_path)

            QMessageBox.information(
                self,
                "نجح المسح",
                f"تم مسح الصورة بنجاح!\nالجودة: {quality}\nالنوع: {scan_type}"
            )

            self.accept()

        except ImportError:
            QMessageBox.warning(
                self,
                "خطأ",
                "مكتبة PIL غير متوفرة.\nيرجى تثبيتها باستخدام: pip install Pillow"
            )
        except Exception as e:
            QMessageBox.warning(
                self,
                "خطأ",
                f"فشل في المسح:\n{str(e)}"
            )

    def get_scanned_images(self):
        """الحصول على الصور الممسوحة"""
        return self.scanned_images
