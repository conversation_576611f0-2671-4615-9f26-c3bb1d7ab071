#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
سكريبت بناء وتصدير نظام أرشفة الكتب الإلكترونية
"""

import os
import sys
import subprocess
from build_config import build_application

def install_build_requirements():
    """تثبيت متطلبات البناء"""
    print("📦 تثبيت متطلبات البناء...")
    
    build_requirements = [
        "pyinstaller",
        "auto-py-to-exe"
    ]
    
    for requirement in build_requirements:
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", requirement])
            print(f"✅ تم تثبيت {requirement}")
        except subprocess.CalledProcessError:
            print(f"❌ فشل في تثبيت {requirement}")
            return False
    
    return True

def create_executable():
    """إنشاء ملف تنفيذي"""
    print("🔨 إنشاء ملف تنفيذي...")
    
    # إعدادات PyInstaller
    pyinstaller_args = [
        "pyinstaller",
        "--onefile",
        "--windowed",
        "--name=BookArchiveSystem",
        "--icon=icon.ico",
        "--add-data=assets;assets",
        "--add-data=icons;icons",
        "--hidden-import=PyQt5.sip",
        "main.py"
    ]
    
    try:
        subprocess.check_call(pyinstaller_args)
        print("✅ تم إنشاء الملف التنفيذي بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في إنشاء الملف التنفيذي: {e}")
        return False

def main():
    """الدالة الرئيسية للبناء"""
    print("🚀 بدء عملية بناء وتصدير البرنامج")
    print("=" * 60)
    
    # بناء التطبيق
    if not build_application():
        print("❌ فشل في بناء التطبيق")
        return False
    
    # تثبيت متطلبات البناء
    if not install_build_requirements():
        print("❌ فشل في تثبيت متطلبات البناء")
        return False
    
    # إنشاء ملف تنفيذي
    if not create_executable():
        print("❌ فشل في إنشاء الملف التنفيذي")
        return False
    
    print("=" * 60)
    print("🎉 تم بناء وتصدير البرنامج بنجاح!")
    print("📁 الملفات متوفرة في:")
    print("  - release/ (حزمة المصدر)")
    print("  - dist/ (الملف التنفيذي)")
    
    return True

if __name__ == "__main__":
    main()
