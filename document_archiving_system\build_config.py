#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
إعدادات بناء وتصدير البرنامج
"""

import os
import sys
import shutil
import zipfile
import json
from datetime import datetime

# معلومات البرنامج
APP_NAME = "نظام أرشفة الكتب الإلكترونية"
APP_VERSION = "1.0.0"
APP_AUTHOR = "فريق التطوير"
APP_DESCRIPTION = "نظام شامل لإدارة وأرشفة الكتب الإلكترونية"

# مجلدات البناء
BUILD_DIR = "build"
DIST_DIR = "dist"
RELEASE_DIR = "release"

# الملفات المطلوبة
REQUIRED_FILES = [
    "main.py",
    "database.py",
    "login.py",
    "book_manager.py",
    "books_view.py",
    "settings_view.py",
    "users_view.py",
    "updater.py",
    "requirements.txt"
]

# الملفات الاختيارية
OPTIONAL_FILES = [
    "README.md",
    "LICENSE",
    "CHANGELOG.md"
]

# المجلدات المطلوبة
REQUIRED_FOLDERS = [
    "assets",
    "icons",
    "data"
]

def create_build_structure():
    """إنشاء هيكل البناء"""
    print("🔧 إنشاء هيكل البناء...")
    
    # إنشاء المجلدات
    for folder in [BUILD_DIR, DIST_DIR, RELEASE_DIR]:
        if os.path.exists(folder):
            shutil.rmtree(folder)
        os.makedirs(folder)
    
    print("✅ تم إنشاء هيكل البناء")

def copy_source_files():
    """نسخ ملفات المصدر"""
    print("📁 نسخ ملفات المصدر...")
    
    build_src = os.path.join(BUILD_DIR, "src")
    os.makedirs(build_src, exist_ok=True)
    
    # نسخ الملفات المطلوبة
    for file_name in REQUIRED_FILES:
        if os.path.exists(file_name):
            shutil.copy2(file_name, build_src)
            print(f"  ✅ {file_name}")
        else:
            print(f"  ⚠️  {file_name} غير موجود")
    
    # نسخ الملفات الاختيارية
    for file_name in OPTIONAL_FILES:
        if os.path.exists(file_name):
            shutil.copy2(file_name, build_src)
            print(f"  ✅ {file_name}")
    
    # نسخ المجلدات
    for folder_name in REQUIRED_FOLDERS:
        if os.path.exists(folder_name):
            shutil.copytree(folder_name, os.path.join(build_src, folder_name))
            print(f"  ✅ {folder_name}/")
    
    print("✅ تم نسخ ملفات المصدر")

def create_version_info():
    """إنشاء معلومات الإصدار"""
    print("📋 إنشاء معلومات الإصدار...")
    
    version_info = {
        "app_name": APP_NAME,
        "version": APP_VERSION,
        "author": APP_AUTHOR,
        "description": APP_DESCRIPTION,
        "build_date": datetime.now().isoformat(),
        "build_number": int(datetime.now().timestamp()),
        "python_version": sys.version,
        "platform": sys.platform,
        "features": [
            "إدارة الكتب الإلكترونية",
            "نظام المستخدمين والصلاحيات",
            "إضافة وتعديل الكتب",
            "إدارة الصور والمرفقات",
            "وظيفة السكانر المتقدمة",
            "البحث والتصفية",
            "نظام التحديث التلقائي",
            "واجهة مستخدم احترافية"
        ],
        "requirements": [
            "PyQt5>=5.15.0",
            "Pillow>=8.0.0",
            "requests>=2.25.0"
        ]
    }
    
    # حفظ معلومات الإصدار
    version_file = os.path.join(BUILD_DIR, "version.json")
    with open(version_file, "w", encoding="utf-8") as f:
        json.dump(version_info, f, ensure_ascii=False, indent=2)
    
    print("✅ تم إنشاء معلومات الإصدار")
    return version_info

def create_installer_script():
    """إنشاء سكريبت التثبيت"""
    print("🛠️ إنشاء سكريبت التثبيت...")
    
    installer_script = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
سكريبت تثبيت نظام أرشفة الكتب الإلكترونية
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def install_requirements():
    """تثبيت المتطلبات"""
    print("📦 تثبيت المتطلبات...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ تم تثبيت المتطلبات بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت المتطلبات: {e}")
        return False

def create_desktop_shortcut():
    """إنشاء اختصار على سطح المكتب"""
    if sys.platform == "win32":
        try:
            import winshell
            from win32com.client import Dispatch
            
            desktop = winshell.desktop()
            path = os.path.join(desktop, "نظام أرشفة الكتب.lnk")
            target = os.path.join(os.getcwd(), "main.py")
            
            shell = Dispatch('WScript.Shell')
            shortcut = shell.CreateShortCut(path)
            shortcut.Targetpath = sys.executable
            shortcut.Arguments = f'"{target}"'
            shortcut.WorkingDirectory = os.getcwd()
            shortcut.IconLocation = os.path.join(os.getcwd(), "icon.ico")
            shortcut.save()
            
            print("✅ تم إنشاء اختصار على سطح المكتب")
        except ImportError:
            print("⚠️ لا يمكن إنشاء اختصار على سطح المكتب")

def main():
    """الدالة الرئيسية للتثبيت"""
    print("🚀 بدء تثبيت نظام أرشفة الكتب الإلكترونية")
    print("=" * 50)
    
    # تثبيت المتطلبات
    if not install_requirements():
        print("❌ فشل التثبيت")
        return False
    
    # إنشاء اختصار
    create_desktop_shortcut()
    
    print("=" * 50)
    print("🎉 تم التثبيت بنجاح!")
    print("يمكنك الآن تشغيل البرنامج من خلال:")
    print(f"python {os.path.join(os.getcwd(), 'main.py')}")
    
    return True

if __name__ == "__main__":
    main()
'''
    
    installer_file = os.path.join(BUILD_DIR, "install.py")
    with open(installer_file, "w", encoding="utf-8") as f:
        f.write(installer_script)
    
    print("✅ تم إنشاء سكريبت التثبيت")

def create_requirements_file():
    """إنشاء ملف المتطلبات"""
    print("📋 إنشاء ملف المتطلبات...")
    
    requirements = [
        "PyQt5>=5.15.0",
        "Pillow>=8.0.0", 
        "requests>=2.25.0"
    ]
    
    requirements_file = os.path.join(BUILD_DIR, "requirements.txt")
    with open(requirements_file, "w", encoding="utf-8") as f:
        f.write("\n".join(requirements))
    
    print("✅ تم إنشاء ملف المتطلبات")

def create_readme():
    """إنشاء ملف README"""
    print("📖 إنشاء ملف README...")
    
    readme_content = f"""# {APP_NAME}

## الوصف
{APP_DESCRIPTION}

## الإصدار
{APP_VERSION}

## المتطلبات
- Python 3.7 أو أحدث
- PyQt5
- Pillow
- requests

## التثبيت
1. فك ضغط الملف
2. تشغيل `python install.py`
3. تشغيل البرنامج `python main.py`

## الميزات
- إدارة الكتب الإلكترونية
- نظام المستخدمين والصلاحيات
- إضافة وتعديل الكتب
- إدارة الصور والمرفقات
- وظيفة السكانر المتقدمة
- البحث والتصفية
- نظام التحديث التلقائي
- واجهة مستخدم احترافية

## الاستخدام
1. تسجيل الدخول (admin / admin123)
2. إدارة الكتب من القائمة الجانبية
3. إضافة كتب جديدة مع الصور
4. استخدام وظيفة السكانر
5. البحث والتصفية

## الدعم الفني
للحصول على الدعم الفني، يرجى التواصل مع فريق التطوير.

## حقوق الطبع والنشر
© 2024 {APP_AUTHOR}. جميع الحقوق محفوظة.
"""
    
    readme_file = os.path.join(BUILD_DIR, "README.md")
    with open(readme_file, "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    print("✅ تم إنشاء ملف README")

def create_release_package():
    """إنشاء حزمة الإصدار"""
    print("📦 إنشاء حزمة الإصدار...")
    
    # اسم الحزمة
    package_name = f"BookArchiveSystem_v{APP_VERSION}_{datetime.now().strftime('%Y%m%d')}.zip"
    package_path = os.path.join(RELEASE_DIR, package_name)
    
    # إنشاء الحزمة
    with zipfile.ZipFile(package_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        # إضافة جميع ملفات البناء
        for root, dirs, files in os.walk(BUILD_DIR):
            for file in files:
                file_path = os.path.join(root, file)
                arc_path = os.path.relpath(file_path, BUILD_DIR)
                zipf.write(file_path, arc_path)
    
    print(f"✅ تم إنشاء الحزمة: {package_name}")
    return package_path

def build_application():
    """بناء التطبيق"""
    print("🏗️ بدء بناء التطبيق...")
    print("=" * 50)
    
    try:
        # إنشاء هيكل البناء
        create_build_structure()
        
        # نسخ ملفات المصدر
        copy_source_files()
        
        # إنشاء معلومات الإصدار
        version_info = create_version_info()
        
        # إنشاء ملفات إضافية
        create_installer_script()
        create_requirements_file()
        create_readme()
        
        # إنشاء حزمة الإصدار
        package_path = create_release_package()
        
        print("=" * 50)
        print("🎉 تم بناء التطبيق بنجاح!")
        print(f"📦 الحزمة: {package_path}")
        print(f"📊 الحجم: {os.path.getsize(package_path) / 1024 / 1024:.2f} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ فشل في بناء التطبيق: {e}")
        return False

if __name__ == "__main__":
    build_application()
