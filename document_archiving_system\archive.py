#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
الأرشفة
تتعامل مع عرض وإدارة الأرشيف
"""

import os
import datetime
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
                            QLineEdit, QTableWidget, QTableWidgetItem, QHeaderView,
                            QMessageBox, QDialog, QFormLayout, QFileDialog, QComboBox,
                            QDateEdit, QCalendarWidget, QTreeWidget, QTreeWidgetItem,
                            QSplitter, QFrame, QTextEdit)
from PyQt5.QtGui import QIcon, QFont
from PyQt5.QtCore import Qt, QDate


class ArchiveWidget(QWidget):
    """واجهة الأرشيف"""

    def __init__(self, db, document_manager):
        super().__init__()

        # تهيئة قاعدة البيانات ومدير المستندات
        self.db = db
        self.document_manager = document_manager

        # المستخدم الحالي
        self.current_user = None

        # إنشاء واجهة المستخدم
        self.init_ui()

        # تحميل الأرشيف
        self.load_archive()

    def set_current_user(self, user):
        """تعيين المستخدم الحالي"""
        self.current_user = user

    def init_ui(self):
        """تهيئة واجهة المستخدم"""

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # عنوان الصفحة
        title_label = QLabel("الأرشيف")
        title_label.setStyleSheet("""
            font-size: 20px;
            font-weight: bold;
            color: #1a3a63;
        """)

        # شريط الأدوات
        toolbar_layout = QHBoxLayout()

        # حقل البحث
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("بحث في الأرشيف...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
            }
        """)

        search_button = QPushButton("بحث")
        search_button.setStyleSheet("""
            QPushButton {
                background-color: #1e81b0;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #166d96;
            }
        """)
        search_button.clicked.connect(self.search_archive)

        # تاريخ البداية
        start_date_label = QLabel("من تاريخ:")
        self.start_date = QDateEdit()
        self.start_date.setCalendarPopup(True)
        self.start_date.setDate(QDate.currentDate().addMonths(-1))

        # تاريخ النهاية
        end_date_label = QLabel("إلى تاريخ:")
        self.end_date = QDateEdit()
        self.end_date.setCalendarPopup(True)
        self.end_date.setDate(QDate.currentDate())

        # زر تطبيق التاريخ
        date_filter_button = QPushButton("تطبيق")
        date_filter_button.setStyleSheet("""
            QPushButton {
                background-color: #16a085;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #138a72;
            }
        """)
        date_filter_button.clicked.connect(self.filter_by_date)

        # إضافة عناصر شريط الأدوات
        toolbar_layout.addWidget(self.search_input)
        toolbar_layout.addWidget(search_button)
        toolbar_layout.addWidget(start_date_label)
        toolbar_layout.addWidget(self.start_date)
        toolbar_layout.addWidget(end_date_label)
        toolbar_layout.addWidget(self.end_date)
        toolbar_layout.addWidget(date_filter_button)

        # إنشاء منطقة العرض الرئيسية
        splitter = QSplitter(Qt.Horizontal)

        # شجرة الأقسام
        self.departments_tree = QTreeWidget()
        self.departments_tree.setHeaderLabel("الأقسام")
        self.departments_tree.setStyleSheet("""
            QTreeWidget {
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }
            QTreeWidget::item {
                padding: 5px;
            }
            QTreeWidget::item:selected {
                background-color: #1e81b0;
                color: white;
            }
        """)
        self.departments_tree.setMinimumWidth(200)
        self.departments_tree.itemClicked.connect(self.department_selected)

        # جدول المستندات
        self.documents_table = QTableWidget()
        self.documents_table.setColumnCount(5)
        self.documents_table.setHorizontalHeaderLabels(["المعرف", "العنوان", "التاريخ", "الحجم", "الإجراءات"])
        self.documents_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.documents_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }
            QHeaderView::section {
                background-color: #1a3a63;
                color: white;
                padding: 8px;
                font-size: 14px;
                font-weight: bold;
                border: none;
            }
        """)

        # إضافة العناصر إلى السبليتر
        splitter.addWidget(self.departments_tree)
        splitter.addWidget(self.documents_table)
        splitter.setSizes([200, 800])

        # إضافة العناصر إلى التخطيط الرئيسي
        main_layout.addWidget(title_label)
        main_layout.addLayout(toolbar_layout)
        main_layout.addWidget(splitter)

    def load_archive(self):
        """تحميل الأرشيف"""
        # تحميل الأقسام
        self.load_departments()

        # تحميل جميع المستندات
        self.load_all_documents()

    def load_departments(self):
        """تحميل الأقسام"""
        # تفريغ شجرة الأقسام
        self.departments_tree.clear()

        # إضافة عنصر "جميع الأقسام"
        all_departments_item = QTreeWidgetItem(self.departments_tree, ["جميع الأقسام"])
        all_departments_item.setData(0, Qt.UserRole, None)

        # الحصول على الأقسام من قاعدة البيانات
        departments = self.db.get_departments()

        # إضافة الأقسام إلى الشجرة
        for department in departments:
            department_item = QTreeWidgetItem(self.departments_tree, [department['name']])
            department_item.setData(0, Qt.UserRole, department['id'])

        # توسيع الشجرة
        self.departments_tree.expandAll()

        # تحديد "جميع الأقسام" افتراضيًا
        self.departments_tree.setCurrentItem(all_departments_item)

    def load_all_documents(self):
        """تحميل جميع المستندات"""
        result = self.document_manager.get_all_documents()

        if result['success']:
            self.display_documents(result['documents'])
        else:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل المستندات: {result.get('error', '')}")

    def load_department_documents(self, department_id):
        """تحميل مستندات قسم معين"""
        result = self.document_manager.get_documents_by_department(department_id)

        if result['success']:
            self.display_documents(result['documents'])
        else:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل مستندات القسم: {result.get('error', '')}")

    def display_documents(self, documents):
        """عرض المستندات في الجدول"""
        # تفريغ الجدول
        self.documents_table.setRowCount(0)

        # إضافة المستندات إلى الجدول
        for row, document in enumerate(documents):
            self.documents_table.insertRow(row)

            # إضافة بيانات المستند
            id_item = QTableWidgetItem(str(document['id']))
            title_item = QTableWidgetItem(document['title'])

            # تنسيق التاريخ
            created_at = datetime.datetime.strptime(document['created_at'], "%Y-%m-%d %H:%M:%S")
            date_str = created_at.strftime("%Y/%m/%d %H:%M")
            date_item = QTableWidgetItem(date_str)

            # تنسيق حجم الملف
            file_size = document['file_size']
            if file_size < 1024:
                size_str = f"{file_size} بايت"
            elif file_size < 1024 * 1024:
                size_str = f"{file_size / 1024:.2f} كيلوبايت"
            else:
                size_str = f"{file_size / (1024 * 1024):.2f} ميجابايت"

            size_item = QTableWidgetItem(size_str)

            self.documents_table.setItem(row, 0, id_item)
            self.documents_table.setItem(row, 1, title_item)
            self.documents_table.setItem(row, 2, date_item)
            self.documents_table.setItem(row, 3, size_item)

            # إضافة أزرار الإجراءات
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(0, 0, 0, 0)

            view_button = QPushButton("عرض")
            view_button.setStyleSheet("""
                QPushButton {
                    background-color: #1e81b0;
                    color: white;
                    border: none;
                    padding: 5px 10px;
                    border-radius: 3px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #166d96;
                }
            """)

            info_button = QPushButton("تفاصيل")
            info_button.setStyleSheet("""
                QPushButton {
                    background-color: #16a085;
                    color: white;
                    border: none;
                    padding: 5px 10px;
                    border-radius: 3px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #138a72;
                }
            """)

            # ربط الأزرار بالإجراءات
            document_id = document['id']
            view_button.clicked.connect(lambda _, doc_id=document_id: self.view_document(doc_id))
            info_button.clicked.connect(lambda _, doc_id=document_id: self.show_document_info(doc_id))

            actions_layout.addWidget(view_button)
            actions_layout.addWidget(info_button)

            self.documents_table.setCellWidget(row, 4, actions_widget)

    def department_selected(self, item, column):
        """تم تحديد قسم من الشجرة"""
        department_id = item.data(0, Qt.UserRole)

        if department_id is None:
            # تم تحديد "جميع الأقسام"
            self.load_all_documents()
        else:
            # تم تحديد قسم معين
            self.load_department_documents(department_id)

    def search_archive(self):
        """البحث في الأرشيف"""
        search_text = self.search_input.text()

        if not search_text:
            # إذا كان حقل البحث فارغًا، قم بتحميل جميع المستندات
            self.load_all_documents()
            return

        result = self.document_manager.search_documents(search_text)

        if result['success']:
            self.display_documents(result['documents'])
        else:
            QMessageBox.warning(self, "خطأ", f"فشل في البحث عن المستندات: {result.get('error', '')}")

    def filter_by_date(self):
        """تصفية المستندات حسب التاريخ"""
        start_date = self.start_date.date().toString("yyyy-MM-dd")
        end_date = self.end_date.date().toString("yyyy-MM-dd")

        # الحصول على المستندات من قاعدة البيانات
        try:
            self.db.cursor.execute(
                """SELECT * FROM documents
                   WHERE date(created_at) BETWEEN ? AND ?
                   ORDER BY created_at DESC""",
                (start_date, end_date)
            )

            documents = []
            for row in self.db.cursor.fetchall():
                doc = dict(row)
                if 'metadata' in doc and doc['metadata']:
                    import json
                    doc['metadata'] = json.loads(doc['metadata'])
                documents.append(doc)

            self.display_documents(documents)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تصفية المستندات حسب التاريخ: {str(e)}")

    def view_document(self, document_id):
        """عرض المستند"""
        result = self.document_manager.get_document_file_path(document_id)

        if not result['success']:
            QMessageBox.warning(self, "خطأ", f"فشل في فتح المستند: {result.get('error', '')}")
            return

        file_path = result['file_path']

        try:
            # فتح الملف باستخدام التطبيق الافتراضي
            if os.name == 'nt':  # Windows
                os.startfile(file_path)
            elif os.name == 'posix':  # macOS, Linux
                import subprocess
                subprocess.call(('xdg-open', file_path))
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في فتح المستند: {str(e)}")

    def show_document_info(self, document_id):
        """عرض تفاصيل المستند"""
        result = self.document_manager.get_document(document_id)

        if not result['success']:
            QMessageBox.warning(self, "خطأ", f"فشل في الحصول على تفاصيل المستند: {result.get('error', '')}")
            return

        document = result['document']

        # إنشاء نافذة تفاصيل المستند
        dialog = DocumentInfoDialog(self, document, self.db)
        dialog.exec()


class DocumentInfoDialog(QDialog):
    """نافذة تفاصيل المستند"""

    def __init__(self, parent, document, db):
        super().__init__(parent)

        self.document = document
        self.db = db

        # تعيين عنوان النافذة
        self.setWindowTitle(f"تفاصيل المستند: {document['title']}")
        self.setMinimumSize(500, 400)

        # إنشاء واجهة المستخدم
        self.init_ui()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # عنوان المستند
        title_label = QLabel(self.document['title'])
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #1a3a63;
        """)

        # تفاصيل المستند
        details_frame = QFrame()
        details_frame.setFrameShape(QFrame.StyledPanel)
        details_frame.setStyleSheet("""
            QFrame {
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
                padding: 10px;
            }
        """)

        details_layout = QFormLayout(details_frame)

        # المعرف
        id_label = QLabel(f"{self.document['id']}")
        details_layout.addRow("المعرف:", id_label)

        # الوصف
        description_label = QLabel(self.document['description'] or "لا يوجد وصف")
        description_label.setWordWrap(True)
        details_layout.addRow("الوصف:", description_label)

        # القسم
        department_name = "غير محدد"
        if self.document['department_id']:
            self.db.cursor.execute("SELECT name FROM departments WHERE id = ?", (self.document['department_id'],))
            department = self.db.cursor.fetchone()
            if department:
                department_name = department['name']

        department_label = QLabel(department_name)
        details_layout.addRow("القسم:", department_label)

        # تاريخ الإنشاء
        created_at = datetime.datetime.strptime(self.document['created_at'], "%Y-%m-%d %H:%M:%S")
        date_str = created_at.strftime("%Y/%m/%d %H:%M")
        date_label = QLabel(date_str)
        details_layout.addRow("تاريخ الإنشاء:", date_label)

        # حجم الملف
        file_size = self.document['file_size']
        if file_size < 1024:
            size_str = f"{file_size} بايت"
        elif file_size < 1024 * 1024:
            size_str = f"{file_size / 1024:.2f} كيلوبايت"
        else:
            size_str = f"{file_size / (1024 * 1024):.2f} ميجابايت"

        size_label = QLabel(size_str)
        details_layout.addRow("حجم الملف:", size_label)

        # نوع الملف
        file_type_label = QLabel(self.document['file_type'] or "غير محدد")
        details_layout.addRow("نوع الملف:", file_type_label)

        # مسار الملف
        file_path_label = QLabel(self.document['file_path'])
        file_path_label.setWordWrap(True)
        details_layout.addRow("مسار الملف:", file_path_label)

        # المستخدم الذي أضاف المستند
        user_name = "غير محدد"
        if self.document['created_by']:
            self.db.cursor.execute("SELECT username, full_name FROM users WHERE id = ?", (self.document['created_by'],))
            user = self.db.cursor.fetchone()
            if user:
                user_name = user['full_name'] or user['username']

        user_label = QLabel(user_name)
        details_layout.addRow("تمت الإضافة بواسطة:", user_label)

        # البيانات الوصفية
        metadata_text = QTextEdit()
        metadata_text.setReadOnly(True)

        if 'metadata' in self.document and self.document['metadata']:
            import json
            metadata = self.document['metadata']
            if isinstance(metadata, str):
                try:
                    metadata = json.loads(metadata)
                except:
                    metadata = {}

            metadata_str = json.dumps(metadata, ensure_ascii=False, indent=4)
            metadata_text.setText(metadata_str)
        else:
            metadata_text.setText("لا توجد بيانات وصفية")

        details_layout.addRow("البيانات الوصفية:", metadata_text)

        # زر الإغلاق
        close_button = QPushButton("إغلاق")
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        close_button.clicked.connect(self.accept)

        # إضافة العناصر إلى التخطيط الرئيسي
        main_layout.addWidget(title_label)
        main_layout.addWidget(details_frame)
        main_layout.addWidget(close_button, 0, Qt.AlignmentFlag.AlignCenter)
