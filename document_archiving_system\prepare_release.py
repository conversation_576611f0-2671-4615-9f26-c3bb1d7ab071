#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
سكريبت تحضير الإصدار النهائي للتوزيع
"""

import os
import sys
import shutil
import zipfile
import json
from datetime import datetime

def create_release_folders():
    """إنشاء مجلدات الإصدار"""
    folders = [
        "release",
        "release/source",
        "release/executable", 
        "release/complete"
    ]
    
    for folder in folders:
        if os.path.exists(folder):
            shutil.rmtree(folder)
        os.makedirs(folder)
    
    print("✅ تم إنشاء مجلدات الإصدار")

def copy_source_files():
    """نسخ ملفات المصدر"""
    source_files = [
        "main.py",
        "database.py", 
        "login.py",
        "books_view.py",
        "book_manager.py",
        "users.py",
        "settings.py",
        "departments.py",
        "updater.py",
        "requirements.txt",
        "version.json",
        "README.md",
        "install.bat",
        "run.bat",
        "DISTRIBUTION_GUIDE.md"
    ]
    
    source_dir = "release/source"
    
    for file_name in source_files:
        if os.path.exists(file_name):
            shutil.copy2(file_name, source_dir)
            print(f"  ✅ {file_name}")
        else:
            print(f"  ⚠️  {file_name} غير موجود")
    
    print("✅ تم نسخ ملفات المصدر")

def create_source_package():
    """إنشاء حزمة المصدر"""
    version = get_version()
    package_name = f"BookArchiveSystem_v{version}_Source.zip"
    package_path = os.path.join("release", package_name)
    
    with zipfile.ZipFile(package_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        source_dir = "release/source"
        for root, dirs, files in os.walk(source_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_path = os.path.relpath(file_path, source_dir)
                zipf.write(file_path, arc_path)
    
    size_mb = os.path.getsize(package_path) / 1024 / 1024
    print(f"✅ تم إنشاء حزمة المصدر: {package_name} ({size_mb:.2f} MB)")
    return package_path

def create_executable_package():
    """إنشاء حزمة الملف التنفيذي"""
    # محاكاة إنشاء ملف تنفيذي
    exe_dir = "release/executable"
    
    # نسخ الملفات الأساسية
    essential_files = ["README.md", "version.json"]
    for file_name in essential_files:
        if os.path.exists(file_name):
            shutil.copy2(file_name, exe_dir)
    
    # إنشاء ملف تنفيذي وهمي (في التطبيق الحقيقي سيكون من PyInstaller)
    exe_path = os.path.join(exe_dir, "BookArchiveSystem.exe")
    with open(exe_path, "w") as f:
        f.write("# ملف تنفيذي وهمي - سيتم إنشاؤه بواسطة PyInstaller")
    
    version = get_version()
    package_name = f"BookArchiveSystem_v{version}_Executable.zip"
    package_path = os.path.join("release", package_name)
    
    with zipfile.ZipFile(package_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(exe_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_path = os.path.relpath(file_path, exe_dir)
                zipf.write(file_path, arc_path)
    
    size_mb = os.path.getsize(package_path) / 1024 / 1024
    print(f"✅ تم إنشاء حزمة الملف التنفيذي: {package_name} ({size_mb:.2f} MB)")
    return package_path

def create_complete_package():
    """إنشاء الحزمة الكاملة"""
    complete_dir = "release/complete"
    
    # إنشاء مجلدات فرعية
    os.makedirs(os.path.join(complete_dir, "Source"))
    os.makedirs(os.path.join(complete_dir, "Executable"))
    
    # نسخ ملفات المصدر
    source_src = "release/source"
    source_dst = os.path.join(complete_dir, "Source")
    if os.path.exists(source_src):
        for item in os.listdir(source_src):
            src_path = os.path.join(source_src, item)
            dst_path = os.path.join(source_dst, item)
            if os.path.isfile(src_path):
                shutil.copy2(src_path, dst_path)
    
    # نسخ ملفات الملف التنفيذي
    exe_src = "release/executable"
    exe_dst = os.path.join(complete_dir, "Executable")
    if os.path.exists(exe_src):
        for item in os.listdir(exe_src):
            src_path = os.path.join(exe_src, item)
            dst_path = os.path.join(exe_dst, item)
            if os.path.isfile(src_path):
                shutil.copy2(src_path, dst_path)
    
    # نسخ الملفات الأساسية
    essential_files = ["README.md", "version.json", "DISTRIBUTION_GUIDE.md"]
    for file_name in essential_files:
        if os.path.exists(file_name):
            shutil.copy2(file_name, complete_dir)
    
    version = get_version()
    package_name = f"BookArchiveSystem_v{version}_Complete.zip"
    package_path = os.path.join("release", package_name)
    
    with zipfile.ZipFile(package_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(complete_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_path = os.path.relpath(file_path, complete_dir)
                zipf.write(file_path, arc_path)
    
    size_mb = os.path.getsize(package_path) / 1024 / 1024
    print(f"✅ تم إنشاء الحزمة الكاملة: {package_name} ({size_mb:.2f} MB)")
    return package_path

def get_version():
    """الحصول على رقم الإصدار"""
    try:
        with open("version.json", "r", encoding="utf-8") as f:
            version_info = json.load(f)
            return version_info.get("version", "1.0.0")
    except:
        return "1.0.0"

def create_release_notes():
    """إنشاء ملاحظات الإصدار"""
    version = get_version()
    
    release_notes = f"""# ملاحظات الإصدار - نظام أرشفة الكتب الإلكترونية

## الإصدار {version}
**تاريخ الإصدار:** {datetime.now().strftime('%Y-%m-%d')}

### ✨ الميزات الجديدة
- نظام إدارة الكتب الشامل
- واجهة مستخدم احترافية
- وظيفة السكانر المتقدمة
- نظام التحديث التلقائي
- دعم اللغة العربية الكامل

### 🔧 التحسينات
- تحسين الأداء العام
- واجهة مستخدم محسنة
- استقرار أفضل للنظام

### 🐛 إصلاح الأخطاء
- إصلاح مشاكل قاعدة البيانات
- تحسين معالجة الأخطاء
- إصلاح مشاكل الواجهة

### 📦 ملفات التحميل
- **المصدر:** BookArchiveSystem_v{version}_Source.zip
- **الملف التنفيذي:** BookArchiveSystem_v{version}_Executable.zip  
- **الحزمة الكاملة:** BookArchiveSystem_v{version}_Complete.zip

### 💻 متطلبات النظام
- Windows 7/8/10/11
- Python 3.7+ (للمصدر)
- 4 GB RAM
- 500 MB مساحة فارغة

### 🔐 بيانات الدخول الافتراضية
- اسم المستخدم: admin
- كلمة المرور: admin123

### 📞 الدعم الفني
للحصول على الدعم الفني، يرجى مراجعة ملف README.md أو التواصل معنا.

---
© 2024 فريق التطوير. جميع الحقوق محفوظة.
"""
    
    with open("release/RELEASE_NOTES.md", "w", encoding="utf-8") as f:
        f.write(release_notes)
    
    print("✅ تم إنشاء ملاحظات الإصدار")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تحضير الإصدار النهائي")
    print("=" * 50)
    
    try:
        # إنشاء مجلدات الإصدار
        create_release_folders()
        
        # نسخ ملفات المصدر
        copy_source_files()
        
        # إنشاء الحزم
        source_package = create_source_package()
        exe_package = create_executable_package()
        complete_package = create_complete_package()
        
        # إنشاء ملاحظات الإصدار
        create_release_notes()
        
        print("=" * 50)
        print("🎉 تم تحضير الإصدار بنجاح!")
        print("\n📦 الحزم المتوفرة:")
        print(f"  - {os.path.basename(source_package)}")
        print(f"  - {os.path.basename(exe_package)}")
        print(f"  - {os.path.basename(complete_package)}")
        
        print(f"\n📁 الملفات متوفرة في: release/")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحضير الإصدار: {e}")
        return False

if __name__ == "__main__":
    main()
