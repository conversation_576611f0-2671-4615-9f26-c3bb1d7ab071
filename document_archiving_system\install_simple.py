#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
سكريبت تثبيت مبسط لنظام أرشفة الكتب الإلكترونية
"""

import subprocess
import sys
import os

def install_package(package):
    """تثبيت حزمة Python"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ تم تثبيت {package} بنجاح")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ فشل في تثبيت {package}")
        return False

def check_python_version():
    """فحص إصدار Python"""
    version = sys.version_info
    if version.major >= 3 and version.minor >= 7:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} متوفر")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} قديم جداً")
        print("يرجى تثبيت Python 3.7 أو أحدث")
        return False

def main():
    """الدالة الرئيسية للتثبيت"""
    print("🚀 بدء تثبيت نظام أرشفة الكتب الإلكترونية")
    print("=" * 50)
    
    # فحص إصدار Python
    if not check_python_version():
        return False
    
    # قائمة المتطلبات الأساسية
    essential_packages = [
        "PyQt5",
        "Pillow", 
        "python-dateutil"
    ]
    
    # قائمة المتطلبات الاختيارية
    optional_packages = [
        "requests"
    ]
    
    print("\n📦 تثبيت المتطلبات الأساسية...")
    essential_success = True
    for package in essential_packages:
        if not install_package(package):
            essential_success = False
    
    print("\n📦 تثبيت المتطلبات الاختيارية...")
    for package in optional_packages:
        install_package(package)  # لا نتوقف عند فشل المتطلبات الاختيارية
    
    if essential_success:
        print("\n" + "=" * 50)
        print("🎉 تم التثبيت بنجاح!")
        print("\n📋 معلومات مهمة:")
        print("• يمكنك تشغيل البرنامج: python main.py")
        print("• بيانات الدخول: admin / admin123")
        print("• إذا لم تعمل التحديثات، فهذا طبيعي")
        print("• جميع الوظائف الأساسية ستعمل بشكل طبيعي")
        
        # اختبار تشغيل البرنامج
        print("\n🧪 اختبار تشغيل البرنامج...")
        try:
            import PyQt5
            print("✅ PyQt5 يعمل بشكل صحيح")
            
            from PIL import Image
            print("✅ Pillow يعمل بشكل صحيح")
            
            print("\n✅ جميع المكونات الأساسية جاهزة!")
            print("يمكنك الآن تشغيل: python main.py")
            
        except ImportError as e:
            print(f"⚠️ تحذير: {e}")
            print("قد تحتاج لإعادة تثبيت المكتبات")
        
        return True
    else:
        print("\n" + "=" * 50)
        print("❌ فشل في تثبيت المتطلبات الأساسية")
        print("\n🔧 حلول مقترحة:")
        print("1. تشغيل Command Prompt كمدير")
        print("2. التأكد من الاتصال بالإنترنت")
        print("3. تحديث pip: python -m pip install --upgrade pip")
        print("4. استخدام: pip install --user [package_name]")
        
        return False

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️ تم إيقاف التثبيت بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
    
    input("\nاضغط Enter للخروج...")
