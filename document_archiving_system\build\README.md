# نظام أرشفة الكتب الإلكترونية

## الوصف
نظام شامل لإدارة وأرشفة الكتب الإلكترونية

## الإصدار
1.0.0

## المتطلبات
- Python 3.7 أو أحدث
- PyQt5
- Pillow
- requests

## التثبيت
1. فك ضغط الملف
2. تشغيل `python install.py`
3. تشغيل البرنامج `python main.py`

## الميزات
- إدارة الكتب الإلكترونية
- نظام المستخدمين والصلاحيات
- إضافة وتعديل الكتب
- إدارة الصور والمرفقات
- وظيفة السكانر المتقدمة
- البحث والتصفية
- نظام التحديث التلقائي
- واجهة مستخدم احترافية

## الاستخدام
1. تسجيل الدخول (admin / admin123)
2. إدارة الكتب من القائمة الجانبية
3. إضافة كتب جديدة مع الصور
4. استخدام وظيفة السكانر
5. البحث والتصفية

## الدعم الفني
للحصول على الدعم الفني، يرجى التواصل مع فريق التطوير.

## حقوق الطبع والنشر
© 2024 فريق التطوير. جميع الحقوق محفوظة.
