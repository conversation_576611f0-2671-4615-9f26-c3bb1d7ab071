@echo off
echo ========================================
echo   تثبيت نظام أرشفة الكتب الإلكترونية
echo   الإصدار 1.0.0
echo ========================================
echo.

echo جاري تثبيت المتطلبات...
echo.

pip install -r requirements.txt

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo   تم التثبيت بنجاح!
    echo ========================================
    echo.
    echo يمكنك الآن تشغيل البرنامج بالطرق التالية:
    echo 1. تشغيل run.bat
    echo 2. تشغيل python main.py
    echo.
    echo بيانات الدخول الافتراضية:
    echo اسم المستخدم: admin
    echo كلمة المرور: admin123
    echo.
) else (
    echo.
    echo ========================================
    echo   فشل في التثبيت!
    echo ========================================
    echo.
    echo تأكد من:
    echo 1. تثبيت Python 3.7 أو أحدث
    echo 2. الاتصال بالإنترنت
    echo 3. صلاحيات المدير
    echo.
)

pause
