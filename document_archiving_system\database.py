#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
وحدة قاعدة البيانات
تتعامل مع تخزين واسترجاع البيانات للمستندات والأقسام والمستخدمين
"""

import os
import sqlite3
import json
from datetime import datetime


class Database:
    """فئة قاعدة البيانات للتعامل مع تخزين واسترجاع البيانات"""

    def __init__(self, db_path=None):
        """تهيئة قاعدة البيانات"""
        if db_path is None:
            # استخدام المسار الافتراضي في مجلد البرنامج
            current_dir = os.path.dirname(os.path.abspath(__file__))
            self.db_path = os.path.join(current_dir, "database.db")
        else:
            self.db_path = db_path

        self.conn = None
        self.cursor = None
        self.connect()
        self.create_tables()

    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.conn = sqlite3.connect(self.db_path)
            self.conn.row_factory = sqlite3.Row  # للحصول على النتائج كقاموس
            self.cursor = self.conn.cursor()
            return True
        except sqlite3.Error as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            return False

    def close(self):
        """إغلاق الاتصال بقاعدة البيانات"""
        if self.conn:
            self.conn.close()

    def create_tables(self):
        """إنشاء جداول قاعدة البيانات إذا لم تكن موجودة"""
        try:
            # جدول الأقسام
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS departments (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # جدول المستخدمين
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT NOT NULL UNIQUE,
                    password TEXT NOT NULL,
                    full_name TEXT,
                    email TEXT,
                    role TEXT DEFAULT 'user',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # جدول أنواع الكتب
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS book_types (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL UNIQUE,
                    description TEXT,
                    fields TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # جدول الكتب
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS books (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    book_number TEXT NOT NULL,
                    book_type_id INTEGER NOT NULL,
                    date TEXT NOT NULL,
                    notes TEXT,
                    piece_number TEXT,
                    district_name TEXT,
                    department_id INTEGER,
                    created_by INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (book_type_id) REFERENCES book_types (id),
                    FOREIGN KEY (department_id) REFERENCES departments (id),
                    FOREIGN KEY (created_by) REFERENCES users (id)
                )
            ''')

            # جدول صور الكتب
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS book_images (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    book_id INTEGER NOT NULL,
                    image_path TEXT NOT NULL,
                    image_name TEXT,
                    image_size INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (book_id) REFERENCES books (id) ON DELETE CASCADE
                )
            ''')

            # جدول المستندات (للتوافق مع النظام القديم)
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS documents (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT NOT NULL,
                    description TEXT,
                    file_path TEXT,
                    file_size INTEGER,
                    file_type TEXT,
                    department_id INTEGER,
                    created_by INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    metadata TEXT,
                    FOREIGN KEY (department_id) REFERENCES departments (id),
                    FOREIGN KEY (created_by) REFERENCES users (id)
                )
            ''')

            # جدول الأضابير (المجلدات)
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS folders (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    description TEXT,
                    department_id INTEGER,
                    created_by INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (department_id) REFERENCES departments (id),
                    FOREIGN KEY (created_by) REFERENCES users (id)
                )
            ''')

            # جدول العلاقة بين المستندات والأضابير
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS document_folder (
                    document_id INTEGER,
                    folder_id INTEGER,
                    PRIMARY KEY (document_id, folder_id),
                    FOREIGN KEY (document_id) REFERENCES documents (id),
                    FOREIGN KEY (folder_id) REFERENCES folders (id)
                )
            ''')

            # إنشاء مستخدم افتراضي إذا لم يكن هناك مستخدمين
            self.cursor.execute("SELECT COUNT(*) FROM users")
            if self.cursor.fetchone()[0] == 0:
                self.cursor.execute('''
                    INSERT INTO users (username, password, full_name, role)
                    VALUES (?, ?, ?, ?)
                ''', ('admin', 'admin123', 'مدير النظام', 'admin'))

            # إنشاء قسم افتراضي إذا لم تكن هناك أقسام
            self.cursor.execute("SELECT COUNT(*) FROM departments")
            if self.cursor.fetchone()[0] == 0:
                self.cursor.execute('''
                    INSERT INTO departments (name, description)
                    VALUES (?, ?), (?, ?)
                ''', ('الإدارة', 'قسم الإدارة العامة', 'الموارد البشرية', 'قسم الموارد البشرية'))

            # إنشاء أنواع الكتب الافتراضية
            self.cursor.execute("SELECT COUNT(*) FROM book_types")
            if self.cursor.fetchone()[0] == 0:
                self.cursor.execute('''
                    INSERT INTO book_types (name, description, fields)
                    VALUES (?, ?, ?), (?, ?, ?)
                ''', (
                    'كتاب عادي', 'كتاب عادي يحتوي على رقم الكتاب والتاريخ والملاحظات',
                    '["book_number", "date", "notes"]',
                    'تعارض خدمات', 'كتاب تعارض خدمات يحتوي على رقم الكتاب والتاريخ ورقم القطعة واسم المقاطعة والملاحظات',
                    '["book_number", "date", "piece_number", "district_name", "notes"]'
                ))

            self.conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"خطأ في إنشاء الجداول: {e}")
            return False

    # وظائف إدارة الأقسام
    def get_departments(self):
        """الحصول على جميع الأقسام"""
        try:
            self.cursor.execute("SELECT * FROM departments ORDER BY name")
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع الأقسام: {e}")
            return []

    def add_department(self, name, description=""):
        """إضافة قسم جديد"""
        try:
            self.cursor.execute(
                "INSERT INTO departments (name, description) VALUES (?, ?)",
                (name, description)
            )
            self.conn.commit()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"خطأ في إضافة قسم: {e}")
            return None

    # وظائف إدارة المستخدمين
    def get_users(self):
        """الحصول على جميع المستخدمين"""
        try:
            self.cursor.execute("SELECT id, username, full_name, email, role FROM users")
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع المستخدمين: {e}")
            return []

    def add_user(self, username, password, full_name="", email="", role="user"):
        """إضافة مستخدم جديد"""
        try:
            self.cursor.execute(
                "INSERT INTO users (username, password, full_name, email, role) VALUES (?, ?, ?, ?, ?)",
                (username, password, full_name, email, role)
            )
            self.conn.commit()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"خطأ في إضافة مستخدم: {e}")
            return None

    def authenticate_user(self, username, password):
        """التحقق من صحة بيانات المستخدم"""
        try:
            self.cursor.execute(
                "SELECT * FROM users WHERE username = ? AND password = ?",
                (username, password)
            )
            user = self.cursor.fetchone()
            return dict(user) if user else None
        except sqlite3.Error as e:
            print(f"خطأ في التحقق من المستخدم: {e}")
            return None

    # وظائف إدارة المستندات
    def add_document(self, title, file_path, description="", department_id=None, created_by=None,
                    file_size=0, file_type="", metadata=None):
        """إضافة مستند جديد"""
        try:
            if metadata is None:
                metadata = {}

            self.cursor.execute(
                """INSERT INTO documents
                   (title, description, file_path, file_size, file_type, department_id, created_by, metadata)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?)""",
                (title, description, file_path, file_size, file_type, department_id, created_by, json.dumps(metadata))
            )
            self.conn.commit()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"خطأ في إضافة مستند: {e}")
            return None

    def get_documents(self, department_id=None):
        """الحصول على المستندات"""
        try:
            query = "SELECT * FROM documents"
            params = []

            if department_id:
                query += " WHERE department_id = ?"
                params.append(department_id)

            query += " ORDER BY created_at DESC"

            self.cursor.execute(query, params)
            documents = []
            for row in self.cursor.fetchall():
                doc = dict(row)
                if 'metadata' in doc and doc['metadata']:
                    doc['metadata'] = json.loads(doc['metadata'])
                documents.append(doc)
            return documents
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع المستندات: {e}")
            return []

    def search_documents(self, search_term):
        """البحث في المستندات"""
        try:
            search_pattern = f"%{search_term}%"
            self.cursor.execute(
                """SELECT * FROM documents
                   WHERE title LIKE ? OR description LIKE ?
                   ORDER BY created_at DESC""",
                (search_pattern, search_pattern)
            )
            documents = []
            for row in self.cursor.fetchall():
                doc = dict(row)
                if 'metadata' in doc and doc['metadata']:
                    doc['metadata'] = json.loads(doc['metadata'])
                documents.append(doc)
            return documents
        except sqlite3.Error as e:
            print(f"خطأ في البحث عن المستندات: {e}")
            return []

    # وظائف إدارة أنواع الكتب
    def get_book_types(self):
        """الحصول على جميع أنواع الكتب"""
        try:
            self.cursor.execute("SELECT * FROM book_types ORDER BY name")
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع أنواع الكتب: {e}")
            return []

    # وظائف إدارة الكتب
    def add_book(self, book_number, book_type_id, date, notes="", piece_number="",
                 district_name="", department_id=None, created_by=None):
        """إضافة كتاب جديد"""
        try:
            self.cursor.execute(
                """INSERT INTO books
                   (book_number, book_type_id, date, notes, piece_number, district_name, department_id, created_by)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?)""",
                (book_number, book_type_id, date, notes, piece_number, district_name, department_id, created_by)
            )
            self.conn.commit()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"خطأ في إضافة كتاب: {e}")
            return None

    def get_books(self, department_id=None, book_type_id=None):
        """الحصول على الكتب"""
        try:
            query = """SELECT b.*, bt.name as book_type_name, d.name as department_name, u.full_name as created_by_name
                      FROM books b
                      LEFT JOIN book_types bt ON b.book_type_id = bt.id
                      LEFT JOIN departments d ON b.department_id = d.id
                      LEFT JOIN users u ON b.created_by = u.id"""
            params = []
            conditions = []

            if department_id:
                conditions.append("b.department_id = ?")
                params.append(department_id)

            if book_type_id:
                conditions.append("b.book_type_id = ?")
                params.append(book_type_id)

            if conditions:
                query += " WHERE " + " AND ".join(conditions)

            query += " ORDER BY b.created_at DESC"

            self.cursor.execute(query, params)
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع الكتب: {e}")
            return []

    def search_books(self, search_term):
        """البحث في الكتب"""
        try:
            search_pattern = f"%{search_term}%"
            self.cursor.execute(
                """SELECT b.*, bt.name as book_type_name, d.name as department_name, u.full_name as created_by_name
                   FROM books b
                   LEFT JOIN book_types bt ON b.book_type_id = bt.id
                   LEFT JOIN departments d ON b.department_id = d.id
                   LEFT JOIN users u ON b.created_by = u.id
                   WHERE b.book_number LIKE ? OR b.notes LIKE ? OR b.piece_number LIKE ? OR b.district_name LIKE ?
                   ORDER BY b.created_at DESC""",
                (search_pattern, search_pattern, search_pattern, search_pattern)
            )
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في البحث عن الكتب: {e}")
            return []

    def add_book_image(self, book_id, image_path, image_name, image_size):
        """إضافة صورة للكتاب"""
        try:
            self.cursor.execute(
                "INSERT INTO book_images (book_id, image_path, image_name, image_size) VALUES (?, ?, ?, ?)",
                (book_id, image_path, image_name, image_size)
            )
            self.conn.commit()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"خطأ في إضافة صورة الكتاب: {e}")
            return None

    def get_book_images(self, book_id):
        """الحصول على صور الكتاب"""
        try:
            self.cursor.execute("SELECT * FROM book_images WHERE book_id = ? ORDER BY created_at", (book_id,))
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع صور الكتاب: {e}")
            return []

    def get_stats(self):
        """الحصول على إحصائيات النظام"""
        try:
            stats = {}

            # عدد الأقسام
            self.cursor.execute("SELECT COUNT(*) FROM departments")
            stats['departments_count'] = self.cursor.fetchone()[0]

            # عدد المستخدمين
            self.cursor.execute("SELECT COUNT(*) FROM users")
            stats['users_count'] = self.cursor.fetchone()[0]

            # عدد المستندات
            self.cursor.execute("SELECT COUNT(*) FROM documents")
            stats['documents_count'] = self.cursor.fetchone()[0]

            # عدد الكتب
            self.cursor.execute("SELECT COUNT(*) FROM books")
            stats['books_count'] = self.cursor.fetchone()[0]

            # إجمالي حجم الملفات
            self.cursor.execute("SELECT SUM(file_size) FROM documents")
            total_size = self.cursor.fetchone()[0]
            stats['total_size'] = total_size if total_size else 0

            return stats
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع الإحصائيات: {e}")
            return {
                'departments_count': 0,
                'users_count': 0,
                'documents_count': 0,
                'books_count': 0,
                'total_size': 0
            }
