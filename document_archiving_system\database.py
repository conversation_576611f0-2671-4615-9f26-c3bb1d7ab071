#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
وحدة قاعدة البيانات
تتعامل مع تخزين واسترجاع البيانات للمستندات والأقسام والمستخدمين
"""

import os
import sqlite3
import json
from datetime import datetime


class Database:
    """فئة قاعدة البيانات للتعامل مع تخزين واسترجاع البيانات"""
    
    def __init__(self, db_path="database.db"):
        """تهيئة قاعدة البيانات"""
        self.db_path = db_path
        self.conn = None
        self.cursor = None
        self.connect()
        self.create_tables()
    
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.conn = sqlite3.connect(self.db_path)
            self.conn.row_factory = sqlite3.Row  # للحصول على النتائج كقاموس
            self.cursor = self.conn.cursor()
            return True
        except sqlite3.Error as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            return False
    
    def close(self):
        """إغلاق الاتصال بقاعدة البيانات"""
        if self.conn:
            self.conn.close()
    
    def create_tables(self):
        """إنشاء جداول قاعدة البيانات إذا لم تكن موجودة"""
        try:
            # جدول الأقسام
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS departments (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # جدول المستخدمين
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT NOT NULL UNIQUE,
                    password TEXT NOT NULL,
                    full_name TEXT,
                    email TEXT,
                    role TEXT DEFAULT 'user',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # جدول المستندات
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS documents (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT NOT NULL,
                    description TEXT,
                    file_path TEXT,
                    file_size INTEGER,
                    file_type TEXT,
                    department_id INTEGER,
                    created_by INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    metadata TEXT,
                    FOREIGN KEY (department_id) REFERENCES departments (id),
                    FOREIGN KEY (created_by) REFERENCES users (id)
                )
            ''')
            
            # جدول الأضابير (المجلدات)
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS folders (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    description TEXT,
                    department_id INTEGER,
                    created_by INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (department_id) REFERENCES departments (id),
                    FOREIGN KEY (created_by) REFERENCES users (id)
                )
            ''')
            
            # جدول العلاقة بين المستندات والأضابير
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS document_folder (
                    document_id INTEGER,
                    folder_id INTEGER,
                    PRIMARY KEY (document_id, folder_id),
                    FOREIGN KEY (document_id) REFERENCES documents (id),
                    FOREIGN KEY (folder_id) REFERENCES folders (id)
                )
            ''')
            
            # إنشاء مستخدم افتراضي إذا لم يكن هناك مستخدمين
            self.cursor.execute("SELECT COUNT(*) FROM users")
            if self.cursor.fetchone()[0] == 0:
                self.cursor.execute('''
                    INSERT INTO users (username, password, full_name, role)
                    VALUES (?, ?, ?, ?)
                ''', ('admin', 'admin123', 'مدير النظام', 'admin'))
            
            # إنشاء قسم افتراضي إذا لم تكن هناك أقسام
            self.cursor.execute("SELECT COUNT(*) FROM departments")
            if self.cursor.fetchone()[0] == 0:
                self.cursor.execute('''
                    INSERT INTO departments (name, description)
                    VALUES (?, ?), (?, ?)
                ''', ('الإدارة', 'قسم الإدارة العامة', 'الموارد البشرية', 'قسم الموارد البشرية'))
            
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"خطأ في إنشاء الجداول: {e}")
            return False
    
    # وظائف إدارة الأقسام
    def get_departments(self):
        """الحصول على جميع الأقسام"""
        try:
            self.cursor.execute("SELECT * FROM departments ORDER BY name")
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع الأقسام: {e}")
            return []
    
    def add_department(self, name, description=""):
        """إضافة قسم جديد"""
        try:
            self.cursor.execute(
                "INSERT INTO departments (name, description) VALUES (?, ?)",
                (name, description)
            )
            self.conn.commit()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"خطأ في إضافة قسم: {e}")
            return None
    
    # وظائف إدارة المستخدمين
    def get_users(self):
        """الحصول على جميع المستخدمين"""
        try:
            self.cursor.execute("SELECT id, username, full_name, email, role FROM users")
            return [dict(row) for row in self.cursor.fetchall()]
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع المستخدمين: {e}")
            return []
    
    def add_user(self, username, password, full_name="", email="", role="user"):
        """إضافة مستخدم جديد"""
        try:
            self.cursor.execute(
                "INSERT INTO users (username, password, full_name, email, role) VALUES (?, ?, ?, ?, ?)",
                (username, password, full_name, email, role)
            )
            self.conn.commit()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"خطأ في إضافة مستخدم: {e}")
            return None
    
    def authenticate_user(self, username, password):
        """التحقق من صحة بيانات المستخدم"""
        try:
            self.cursor.execute(
                "SELECT * FROM users WHERE username = ? AND password = ?",
                (username, password)
            )
            user = self.cursor.fetchone()
            return dict(user) if user else None
        except sqlite3.Error as e:
            print(f"خطأ في التحقق من المستخدم: {e}")
            return None
    
    # وظائف إدارة المستندات
    def add_document(self, title, file_path, description="", department_id=None, created_by=None, 
                    file_size=0, file_type="", metadata=None):
        """إضافة مستند جديد"""
        try:
            if metadata is None:
                metadata = {}
            
            self.cursor.execute(
                """INSERT INTO documents 
                   (title, description, file_path, file_size, file_type, department_id, created_by, metadata) 
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?)""",
                (title, description, file_path, file_size, file_type, department_id, created_by, json.dumps(metadata))
            )
            self.conn.commit()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"خطأ في إضافة مستند: {e}")
            return None
    
    def get_documents(self, department_id=None):
        """الحصول على المستندات"""
        try:
            query = "SELECT * FROM documents"
            params = []
            
            if department_id:
                query += " WHERE department_id = ?"
                params.append(department_id)
            
            query += " ORDER BY created_at DESC"
            
            self.cursor.execute(query, params)
            documents = []
            for row in self.cursor.fetchall():
                doc = dict(row)
                if 'metadata' in doc and doc['metadata']:
                    doc['metadata'] = json.loads(doc['metadata'])
                documents.append(doc)
            return documents
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع المستندات: {e}")
            return []
    
    def search_documents(self, search_term):
        """البحث في المستندات"""
        try:
            search_pattern = f"%{search_term}%"
            self.cursor.execute(
                """SELECT * FROM documents 
                   WHERE title LIKE ? OR description LIKE ? 
                   ORDER BY created_at DESC""",
                (search_pattern, search_pattern)
            )
            documents = []
            for row in self.cursor.fetchall():
                doc = dict(row)
                if 'metadata' in doc and doc['metadata']:
                    doc['metadata'] = json.loads(doc['metadata'])
                documents.append(doc)
            return documents
        except sqlite3.Error as e:
            print(f"خطأ في البحث عن المستندات: {e}")
            return []
    
    def get_stats(self):
        """الحصول على إحصائيات النظام"""
        try:
            stats = {}
            
            # عدد الأقسام
            self.cursor.execute("SELECT COUNT(*) FROM departments")
            stats['departments_count'] = self.cursor.fetchone()[0]
            
            # عدد المستخدمين
            self.cursor.execute("SELECT COUNT(*) FROM users")
            stats['users_count'] = self.cursor.fetchone()[0]
            
            # عدد المستندات
            self.cursor.execute("SELECT COUNT(*) FROM documents")
            stats['documents_count'] = self.cursor.fetchone()[0]
            
            # إجمالي حجم الملفات
            self.cursor.execute("SELECT SUM(file_size) FROM documents")
            total_size = self.cursor.fetchone()[0]
            stats['total_size'] = total_size if total_size else 0
            
            return stats
        except sqlite3.Error as e:
            print(f"خطأ في استرجاع الإحصائيات: {e}")
            return {
                'departments_count': 0,
                'users_count': 0,
                'documents_count': 0,
                'total_size': 0
            }
