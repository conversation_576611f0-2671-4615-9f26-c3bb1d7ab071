#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
مدير المستندات
يتعامل مع عمليات إدارة المستندات مثل الإضافة والبحث والعرض
"""

import os
import shutil
import datetime
import mimetypes
from pathlib import Path


class DocumentManager:
    """فئة إدارة المستندات"""

    def __init__(self, db, storage_path=None):
        """تهيئة مدير المستندات"""
        self.db = db

        if storage_path is None:
            # استخدام المسار الافتراضي في مجلد البرنامج
            current_dir = os.path.dirname(os.path.abspath(__file__))
            self.storage_path = os.path.join(current_dir, "documents")
        else:
            self.storage_path = storage_path

        # إنشاء مجلد التخزين إذا لم يكن موجودًا
        if not os.path.exists(self.storage_path):
            os.makedirs(self.storage_path)

    def add_document(self, file_path, title, description="", department_id=None, user_id=None, metadata=None):
        """إضافة مستند جديد"""
        try:
            # التحقق من وجود الملف
            if not os.path.exists(file_path):
                return {"success": False, "error": "الملف غير موجود"}

            # الحصول على معلومات الملف
            file_size = os.path.getsize(file_path)
            file_type = mimetypes.guess_type(file_path)[0] or "application/octet-stream"

            # إنشاء مسار التخزين
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = os.path.basename(file_path)
            storage_filename = f"{timestamp}_{filename}"

            # إنشاء مجلد للقسم إذا تم تحديده
            if department_id:
                department_folder = os.path.join(self.storage_path, f"dept_{department_id}")
                if not os.path.exists(department_folder):
                    os.makedirs(department_folder)
                storage_path = os.path.join(department_folder, storage_filename)
            else:
                storage_path = os.path.join(self.storage_path, storage_filename)

            # نسخ الملف إلى مجلد التخزين
            shutil.copy2(file_path, storage_path)

            # إضافة المستند إلى قاعدة البيانات
            document_id = self.db.add_document(
                title=title,
                description=description,
                file_path=storage_path,
                department_id=department_id,
                created_by=user_id,
                file_size=file_size,
                file_type=file_type,
                metadata=metadata
            )

            if document_id:
                return {
                    "success": True,
                    "document_id": document_id,
                    "file_path": storage_path,
                    "file_size": file_size,
                    "file_type": file_type
                }
            else:
                # حذف الملف إذا فشلت إضافته إلى قاعدة البيانات
                if os.path.exists(storage_path):
                    os.remove(storage_path)
                return {"success": False, "error": "فشل في إضافة المستند إلى قاعدة البيانات"}

        except Exception as e:
            return {"success": False, "error": str(e)}

    def get_document(self, document_id):
        """الحصول على مستند بواسطة المعرف"""
        try:
            # استرجاع المستند من قاعدة البيانات
            self.db.cursor.execute("SELECT * FROM documents WHERE id = ?", (document_id,))
            document = self.db.cursor.fetchone()

            if not document:
                return {"success": False, "error": "المستند غير موجود"}

            document_dict = dict(document)

            # التحقق من وجود الملف
            if not os.path.exists(document_dict['file_path']):
                return {
                    "success": True,
                    "document": document_dict,
                    "warning": "ملف المستند غير موجود في نظام الملفات"
                }

            return {"success": True, "document": document_dict}

        except Exception as e:
            return {"success": False, "error": str(e)}

    def search_documents(self, search_term):
        """البحث في المستندات"""
        try:
            documents = self.db.search_documents(search_term)
            return {"success": True, "documents": documents}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def get_documents_by_department(self, department_id):
        """الحصول على المستندات حسب القسم"""
        try:
            documents = self.db.get_documents(department_id=department_id)
            return {"success": True, "documents": documents}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def get_all_documents(self):
        """الحصول على جميع المستندات"""
        try:
            documents = self.db.get_documents()
            return {"success": True, "documents": documents}
        except Exception as e:
            return {"success": False, "error": str(e)}

    def delete_document(self, document_id):
        """حذف مستند"""
        try:
            # استرجاع المستند من قاعدة البيانات
            self.db.cursor.execute("SELECT * FROM documents WHERE id = ?", (document_id,))
            document = self.db.cursor.fetchone()

            if not document:
                return {"success": False, "error": "المستند غير موجود"}

            document_dict = dict(document)
            file_path = document_dict['file_path']

            # حذف الملف إذا كان موجودًا
            if os.path.exists(file_path):
                os.remove(file_path)

            # حذف المستند من قاعدة البيانات
            self.db.cursor.execute("DELETE FROM documents WHERE id = ?", (document_id,))
            self.db.conn.commit()

            return {"success": True}

        except Exception as e:
            return {"success": False, "error": str(e)}

    def get_document_file_path(self, document_id):
        """الحصول على مسار ملف المستند"""
        try:
            self.db.cursor.execute("SELECT file_path FROM documents WHERE id = ?", (document_id,))
            result = self.db.cursor.fetchone()

            if not result:
                return {"success": False, "error": "المستند غير موجود"}

            file_path = result['file_path']

            if not os.path.exists(file_path):
                return {"success": False, "error": "ملف المستند غير موجود في نظام الملفات"}

            return {"success": True, "file_path": file_path}

        except Exception as e:
            return {"success": False, "error": str(e)}

    def get_storage_info(self):
        """الحصول على معلومات التخزين"""
        try:
            # إجمالي عدد الملفات
            total_files = 0
            total_size = 0

            # حساب عدد الملفات وحجمها
            for root, dirs, files in os.walk(self.storage_path):
                total_files += len(files)
                for file in files:
                    file_path = os.path.join(root, file)
                    total_size += os.path.getsize(file_path)

            # تحويل الحجم إلى وحدات مناسبة
            size_mb = total_size / (1024 * 1024)

            return {
                "success": True,
                "total_files": total_files,
                "total_size_bytes": total_size,
                "total_size_mb": round(size_mb, 2)
            }

        except Exception as e:
            return {"success": False, "error": str(e)}
