# دليل استكشاف الأخطاء - نظام أرشفة الكتب الإلكترونية

## 🚨 المشاكل الشائعة وحلولها

### 1. خطأ "ModuleNotFoundError: No module named 'requests'"

#### الأعراض:
```
File "main.py", line 25, in <module>
    from updater import UpdateDialog, get_current_version
File "updater.py", line 11, in <module>
    import requests
ModuleNotFoundError: No module named 'requests'
```

#### الحلول:

**الحل الأول: تثبيت requests**
```bash
pip install requests
```

**الحل الثاني: تثبيت جميع المتطلبات**
```bash
pip install -r requirements.txt
```

**الحل الثالث: استخدام سكريبت التثبيت**
```bash
python install_simple.py
```

**الحل الرابع: تثبيت للمستخدم الحالي**
```bash
pip install --user requests
```

#### ملاحظة مهمة:
البرنامج مصمم للعمل بدون `requests`. إذا لم تتمكن من تثبيتها، ستعمل جميع الوظائف عدا التحديثات التلقائية.

### 2. خطأ "ModuleNotFoundError: No module named 'PyQt5'"

#### الحلول:
```bash
# الحل الأساسي
pip install PyQt5

# إذا فشل، جرب:
pip install --user PyQt5

# أو استخدم conda:
conda install pyqt
```

### 3. مشاكل في واجهة المستخدم

#### الأعراض:
- النوافذ لا تظهر
- النص غير واضح
- الأزرار لا تعمل

#### الحلول:
1. **تحديث PyQt5:**
   ```bash
   pip install --upgrade PyQt5
   ```

2. **فحص دقة الشاشة:**
   - تأكد من أن دقة الشاشة مدعومة
   - جرب تشغيل البرنامج بدقة مختلفة

3. **إعادة تشغيل النظام:**
   - أحياناً تحتاج PyQt5 لإعادة تشغيل

### 4. مشاكل قاعدة البيانات

#### الأعراض:
- خطأ في حفظ البيانات
- فقدان البيانات
- رسائل خطأ SQL

#### الحلول:
1. **حذف قاعدة البيانات:**
   ```bash
   del database.db
   # ثم إعادة تشغيل البرنامج
   ```

2. **فحص الصلاحيات:**
   - تأكد من صلاحيات الكتابة في المجلد

3. **نسخ احتياطي:**
   - انسخ ملف `database.db` قبل أي تغيير

### 5. مشاكل السكانر

#### الأعراض:
- السكانر لا يعمل
- خطأ في معالجة الصور

#### الحلول:
1. **تثبيت Pillow:**
   ```bash
   pip install Pillow
   ```

2. **فحص صيغ الصور:**
   - استخدم صيغ مدعومة: JPG, PNG, BMP

3. **فحص الذاكرة:**
   - تأكد من توفر ذاكرة كافية

### 6. مشاكل التثبيت

#### مشكلة: "pip is not recognized"
```bash
# إضافة Python إلى PATH
# أو استخدام:
python -m pip install [package]
```

#### مشكلة: "Permission denied"
```bash
# تشغيل كمدير أو:
pip install --user [package]
```

#### مشكلة: "SSL Certificate error"
```bash
pip install --trusted-host pypi.org --trusted-host pypi.python.org [package]
```

## 🔧 أدوات التشخيص

### فحص البيئة:
```python
# تشغيل هذا الكود لفحص البيئة
import sys
print("Python version:", sys.version)

try:
    import PyQt5
    print("✅ PyQt5 متوفر")
except ImportError:
    print("❌ PyQt5 غير متوفر")

try:
    import PIL
    print("✅ Pillow متوفر")
except ImportError:
    print("❌ Pillow غير متوفر")

try:
    import requests
    print("✅ requests متوفر")
except ImportError:
    print("❌ requests غير متوفر (اختياري)")
```

### فحص الملفات:
```bash
# تأكد من وجود الملفات الأساسية
dir main.py
dir database.py
dir requirements.txt
```

## 🚀 خطوات التشغيل المضمونة

### الطريقة الآمنة:
1. **فحص Python:**
   ```bash
   python --version
   ```
   يجب أن يكون 3.7 أو أحدث

2. **تثبيت المتطلبات الأساسية:**
   ```bash
   pip install PyQt5 Pillow python-dateutil
   ```

3. **تثبيت المتطلبات الاختيارية:**
   ```bash
   pip install requests
   ```

4. **تشغيل البرنامج:**
   ```bash
   python main.py
   ```

### إذا فشلت الطريقة الآمنة:
1. **استخدم سكريبت التثبيت:**
   ```bash
   python install_simple.py
   ```

2. **أو استخدم ملف batch:**
   ```bash
   install.bat
   ```

## 📞 الحصول على المساعدة

### معلومات مطلوبة عند طلب المساعدة:
1. **إصدار Python:** `python --version`
2. **نظام التشغيل:** Windows 10/11
3. **رسالة الخطأ الكاملة**
4. **الخطوات التي اتبعتها**

### نصائح عامة:
- ✅ تأكد من تثبيت Python 3.7+
- ✅ استخدم Command Prompt كمدير
- ✅ تأكد من الاتصال بالإنترنت
- ✅ أعد تشغيل النظام بعد التثبيت
- ✅ احتفظ بنسخة احتياطية من البيانات

### الملفات المهمة:
- `main.py` - الملف الرئيسي
- `database.db` - قاعدة البيانات (تحتوي على بياناتك)
- `requirements.txt` - قائمة المتطلبات
- `install_simple.py` - سكريبت التثبيت المبسط

---

**ملاحظة:** البرنامج مصمم ليعمل حتى لو فشل تثبيت بعض المكتبات الاختيارية. الوظائف الأساسية ستعمل دائماً.
