#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
واجهة تسجيل الدخول
تتعامل مع عملية تسجيل الدخول والتحقق من المستخدم
"""

import sys
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout,
                            QPushButton, QLabel, QLineEdit, QMessageBox, QFrame,
                            QGraphicsDropShadowEffect, QCheckBox)
from PyQt5.QtGui import QIcon, QFont, QPixmap, QColor
from PyQt5.QtCore import Qt, QSize, QPoint

from database import Database


class LoginWindow(QWidget):
    """نافذة تسجيل الدخول"""

    def __init__(self, on_login_success=None):
        super().__init__()

        # إعداد النافذة
        self.setWindowTitle("تسجيل الدخول - نظام أرشفة الكتب الإلكترونية")
        self.setFixedSize(500, 600)
        self.setStyleSheet("""
            QWidget {
                background-color: transparent;
                font-family: 'Segoe UI', 'Arial', sans-serif;
            }
        """)

        # إزالة إطار النافذة
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)

        # متغيرات السحب
        self.drag_position = None

        # تهيئة قاعدة البيانات
        self.db = Database()

        # دالة رد الاتصال عند نجاح تسجيل الدخول
        self.on_login_success = on_login_success

        # إنشاء واجهة المستخدم
        self.init_ui()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # إنشاء إطار رئيسي مع تأثير الظل
        main_frame = QFrame(self)
        main_frame.setObjectName("mainFrame")
        main_frame.setStyleSheet("""
            #mainFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border-radius: 25px;
                border: 1px solid rgba(255, 255, 255, 0.8);
            }
        """)

        # إضافة تأثير الظل المحسن
        shadow = QGraphicsDropShadowEffect(self)
        shadow.setBlurRadius(30)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(0, 5)
        main_frame.setGraphicsEffect(shadow)

        # تخطيط الإطار الرئيسي
        frame_layout = QVBoxLayout(main_frame)
        frame_layout.setContentsMargins(30, 30, 30, 30)
        frame_layout.setSpacing(20)

        # إضافة شعار التطبيق المحسن
        logo_layout = QHBoxLayout()
        logo_container = QFrame()
        logo_container.setFixedSize(120, 120)
        logo_container.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1a3a63, stop:1 #2c5282);
                border-radius: 60px;
                border: 3px solid rgba(255, 255, 255, 0.3);
            }
        """)

        # إضافة تأثير الظل للشعار
        logo_shadow = QGraphicsDropShadowEffect()
        logo_shadow.setBlurRadius(15)
        logo_shadow.setColor(QColor(0, 0, 0, 50))
        logo_shadow.setOffset(0, 3)
        logo_container.setGraphicsEffect(logo_shadow)

        logo_label = QLabel("📚")
        logo_label.setParent(logo_container)
        logo_label.setGeometry(0, 0, 120, 120)
        logo_label.setStyleSheet("""
            color: white;
            font-size: 50px;
            font-weight: bold;
            background: transparent;
            border: none;
        """)
        logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        logo_layout.addStretch()
        logo_layout.addWidget(logo_container)
        logo_layout.addStretch()

        # عنوان التطبيق
        title_label = QLabel("نظام أرشفة الكتب الإلكترونية")
        title_label.setStyleSheet("""
            font-size: 26px;
            font-weight: bold;
            color: #1a3a63;
            margin-top: 15px;
            margin-bottom: 5px;
        """)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # عنوان فرعي
        subtitle_label = QLabel("إدارة وأرشفة الكتب بطريقة احترافية")
        subtitle_label.setStyleSheet("""
            font-size: 14px;
            color: #666666;
            margin-bottom: 20px;
        """)
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # عنوان تسجيل الدخول
        login_label = QLabel("تسجيل الدخول")
        login_label.setStyleSheet("""
            font-size: 20px;
            font-weight: bold;
            color: #1a3a63;
            margin-bottom: 15px;
            padding: 10px;
            background-color: rgba(26, 58, 99, 0.1);
            border-radius: 8px;
        """)
        login_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # حقل اسم المستخدم
        username_layout = QVBoxLayout()
        username_label = QLabel("اسم المستخدم:")
        username_label.setStyleSheet("""
            font-size: 14px;
            font-weight: bold;
            color: #333333;
            margin-bottom: 5px;
        """)
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        self.username_input.setStyleSheet("""
            QLineEdit {
                padding: 15px;
                border: 2px solid #e0e0e0;
                border-radius: 12px;
                font-size: 16px;
                background-color: #f8f9fa;
                color: #333333;
            }
            QLineEdit:focus {
                border: 2px solid #1a3a63;
                background-color: white;
                box-shadow: 0 0 10px rgba(26, 58, 99, 0.2);
            }
            QLineEdit:hover {
                border: 2px solid #2c5282;
                background-color: white;
            }
        """)
        self.username_input.setMinimumHeight(45)
        self.username_input.setText("admin")  # اسم المستخدم الافتراضي
        username_layout.addWidget(username_label)
        username_layout.addWidget(self.username_input)

        # حقل كلمة المرور
        password_layout = QVBoxLayout()
        password_label = QLabel("كلمة المرور:")
        password_label.setStyleSheet("""
            font-size: 14px;
            font-weight: bold;
            color: #333333;
            margin-bottom: 5px;
        """)
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setStyleSheet("""
            QLineEdit {
                padding: 15px;
                border: 2px solid #e0e0e0;
                border-radius: 12px;
                font-size: 16px;
                background-color: #f8f9fa;
                color: #333333;
            }
            QLineEdit:focus {
                border: 2px solid #1a3a63;
                background-color: white;
                box-shadow: 0 0 10px rgba(26, 58, 99, 0.2);
            }
            QLineEdit:hover {
                border: 2px solid #2c5282;
                background-color: white;
            }
        """)
        self.password_input.setMinimumHeight(45)
        self.password_input.setText("admin123")  # كلمة المرور الافتراضية
        password_layout.addWidget(password_label)
        password_layout.addWidget(self.password_input)

        # تذكر بيانات الدخول
        remember_layout = QHBoxLayout()
        self.remember_checkbox = QCheckBox("تذكر بيانات الدخول")
        self.remember_checkbox.setStyleSheet("""
            QCheckBox {
                font-size: 13px;
                color: #555555;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #e0e0e0;
                border-radius: 4px;
            }
            QCheckBox::indicator:checked {
                background-color: #1a3a63;
                border: 2px solid #1a3a63;
            }
        """)
        remember_layout.addWidget(self.remember_checkbox)
        remember_layout.addStretch()

        # زر تسجيل الدخول
        login_button = QPushButton("تسجيل الدخول")
        login_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1a3a63, stop:1 #2c5282);
                color: white;
                border: none;
                padding: 15px;
                border-radius: 12px;
                font-size: 18px;
                font-weight: bold;
                min-height: 55px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2c5282, stop:1 #3d6aa2);
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #0f2342, stop:1 #1a3a63);
                transform: translateY(0px);
            }
        """)
        login_button.clicked.connect(self.login)

        # أزرار التحكم في النافذة
        window_controls = QHBoxLayout()
        close_button = QPushButton("×")
        close_button.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #777777;
                font-size: 20px;
                font-weight: bold;
                border: none;
                padding: 5px;
            }
            QPushButton:hover {
                color: #e74c3c;
            }
        """)
        close_button.setFixedSize(30, 30)
        close_button.clicked.connect(self.close)
        window_controls.addStretch()
        window_controls.addWidget(close_button)

        # إضافة العناصر إلى تخطيط الإطار
        frame_layout.addLayout(window_controls)
        frame_layout.addLayout(logo_layout)
        frame_layout.addWidget(title_label)
        frame_layout.addWidget(subtitle_label)
        frame_layout.addWidget(login_label)
        frame_layout.addLayout(username_layout)
        frame_layout.addLayout(password_layout)
        frame_layout.addLayout(remember_layout)
        frame_layout.addWidget(login_button)

        # معلومات الإصدار
        version_label = QLabel("الإصدار 1.0 © 2023 جميع الحقوق محفوظة")
        version_label.setStyleSheet("color: #999; font-size: 12px; margin-top: 10px;")
        version_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        frame_layout.addWidget(version_label)

        # إضافة الإطار الرئيسي إلى التخطيط الرئيسي
        main_layout.addWidget(main_frame)

        # إضافة دعم السحب للنافذة
        self.oldPos = None

    def mousePressEvent(self, event):
        """تنفيذ عند الضغط على الماوس"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.oldPos = event.globalPosition().toPoint()

    def mouseMoveEvent(self, event):
        """تنفيذ عند تحريك الماوس"""
        if self.oldPos and event.buttons() == Qt.MouseButton.LeftButton:
            delta = QPoint(event.globalPosition().toPoint() - self.oldPos)
            self.move(self.x() + delta.x(), self.y() + delta.y())
            self.oldPos = event.globalPosition().toPoint()

    def mouseReleaseEvent(self, event):
        """تنفيذ عند ترك زر الماوس"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.oldPos = None

    def login(self):
        """التحقق من بيانات تسجيل الدخول"""
        username = self.username_input.text()
        password = self.password_input.text()

        if not username or not password:
            self.show_error_message("يرجى إدخال اسم المستخدم وكلمة المرور")
            return

        # التحقق من صحة بيانات المستخدم
        user = self.db.authenticate_user(username, password)

        if user:
            if self.on_login_success:
                self.on_login_success(user)
            self.close()
        else:
            self.show_error_message("اسم المستخدم أو كلمة المرور غير صحيحة")

    def show_error_message(self, message):
        """عرض رسالة خطأ بتصميم جميل"""
        error_dialog = QMessageBox(self)
        error_dialog.setWindowTitle("خطأ")
        error_dialog.setText(message)
        error_dialog.setIcon(QMessageBox.Icon.Warning)
        error_dialog.setStandardButtons(QMessageBox.StandardButton.Ok)
        error_dialog.setStyleSheet("""
            QMessageBox {
                background-color: #ffffff;
                font-family: 'Arial';
            }
            QMessageBox QLabel {
                color: #333333;
                font-size: 14px;
            }
            QPushButton {
                background-color: #1a3a63;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 14px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #2c5282;
            }
        """)
        error_dialog.exec()


def main():
    """الدالة الرئيسية لتشغيل نافذة تسجيل الدخول"""
    app = QApplication(sys.argv)

    # تعيين اتجاه التطبيق من اليمين إلى اليسار للغة العربية
    app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

    # تعيين الخط للتطبيق
    font = QFont("Arial", 10)
    app.setFont(font)

    def on_login_success(user):
        print(f"تم تسجيل الدخول بنجاح: {user['username']}")

    window = LoginWindow(on_login_success=on_login_success)
    window.show()

    sys.exit(app.exec())


if __name__ == "__main__":
    main()
