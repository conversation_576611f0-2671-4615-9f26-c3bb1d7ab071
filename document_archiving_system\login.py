#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
واجهة تسجيل الدخول
تتعامل مع عملية تسجيل الدخول والتحقق من المستخدم
"""

import sys
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout,
                            QPushButton, QLabel, QLineEdit, QMessageBox, QFrame,
                            QGraphicsDropShadowEffect, QCheckBox, QDialog)
from PyQt5.QtGui import QIcon, QFont, QPixmap, QColor
from PyQt5.QtCore import Qt, QSize, QPoint

from database import Database


class LoginWindow(QDialog):
    """نافذة تسجيل الدخول"""

    def __init__(self, on_login_success=None):
        super().__init__()

        # إعداد النافذة
        self.setWindowTitle("تسجيل الدخول - نظام أرشفة الكتب الإلكترونية")
        self.setFixedSize(450, 550)
        self.setStyleSheet("""
            QDialog {
                background-color: #f0f0f0;
                font-family: 'Segoe UI', 'Arial', sans-serif;
            }
        """)

        # إبقاء إطار النافذة العادي
        # self.setWindowFlags(Qt.WindowType.FramelessWindowHint)
        # self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)

        # متغيرات السحب
        self.drag_position = None

        # توسيط النافذة
        self.center_window()

        # تهيئة قاعدة البيانات
        self.db = Database()

        # دالة رد الاتصال عند نجاح تسجيل الدخول
        self.on_login_success = on_login_success

        # إنشاء واجهة المستخدم
        self.init_ui()

    def center_window(self):
        """توسيط النافذة في الشاشة"""
        from PyQt5.QtWidgets import QApplication
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )

    def init_ui(self):
        """تهيئة واجهة المستخدم"""

        # التخطيط الرئيسي البسيط
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(20)

        # شعار التطبيق البسيط
        logo_label = QLabel("📚")
        logo_label.setStyleSheet("""
            font-size: 60px;
            color: #1a3a63;
            margin: 10px;
        """)
        logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # عنوان التطبيق
        title_label = QLabel("نظام أرشفة الكتب")
        title_label.setStyleSheet("""
            font-size: 20px;
            font-weight: bold;
            color: #1a3a63;
            margin: 5px 0;
        """)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # عنوان تسجيل الدخول
        login_label = QLabel("تسجيل الدخول")
        login_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #1a3a63;
            margin: 10px 0;
            padding: 8px;
            background-color: rgba(26, 58, 99, 0.1);
            border-radius: 8px;
        """)
        login_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # حقل اسم المستخدم
        username_label = QLabel("اسم المستخدم:")
        username_label.setStyleSheet("""
            font-size: 14px;
            font-weight: bold;
            color: #1a3a63;
            margin: 5px 0;
        """)

        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        self.username_input.setStyleSheet("""
            QLineEdit {
                padding: 12px;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                font-size: 14px;
                background-color: white;
                color: #333333;
            }
            QLineEdit:focus {
                border: 2px solid #1a3a63;
                background-color: white;
            }
        """)
        self.username_input.setMinimumHeight(40)
        self.username_input.setText("admin")

        # حقل كلمة المرور
        password_label = QLabel("كلمة المرور:")
        password_label.setStyleSheet("""
            font-size: 14px;
            font-weight: bold;
            color: #1a3a63;
            margin: 10px 0 5px 0;
        """)

        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setStyleSheet("""
            QLineEdit {
                padding: 12px;
                border: 2px solid #e9ecef;
                border-radius: 8px;
                font-size: 14px;
                background-color: white;
                color: #333333;
            }
            QLineEdit:focus {
                border: 2px solid #1a3a63;
                background-color: white;
            }
        """)
        self.password_input.setMinimumHeight(40)
        self.password_input.setText("admin123")

        # تذكر بيانات الدخول
        remember_layout = QHBoxLayout()
        self.remember_checkbox = QCheckBox("تذكر بيانات الدخول")
        self.remember_checkbox.setStyleSheet("""
            QCheckBox {
                font-size: 13px;
                color: #555555;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #e0e0e0;
                border-radius: 4px;
            }
            QCheckBox::indicator:checked {
                background-color: #1a3a63;
                border: 2px solid #1a3a63;
            }
        """)
        remember_layout.addWidget(self.remember_checkbox)
        remember_layout.addStretch()

        # زر تسجيل الدخول
        login_button = QPushButton("تسجيل الدخول")
        login_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #1a3a63, stop:1 #2c5282);
                color: white;
                border: none;
                padding: 15px;
                border-radius: 12px;
                font-size: 18px;
                font-weight: bold;
                min-height: 55px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2c5282, stop:1 #3d6aa2);
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #0f2342, stop:1 #1a3a63);
                transform: translateY(0px);
            }
        """)
        login_button.clicked.connect(self.login)



        # إضافة العناصر إلى التخطيط الرئيسي
        main_layout.addWidget(logo_label)
        main_layout.addWidget(title_label)
        main_layout.addWidget(login_label)
        main_layout.addWidget(username_label)
        main_layout.addWidget(self.username_input)
        main_layout.addWidget(password_label)
        main_layout.addWidget(self.password_input)
        main_layout.addLayout(remember_layout)
        main_layout.addWidget(login_button)

        # معلومات الإصدار
        version_label = QLabel("الإصدار 1.0 © 2023 جميع الحقوق محفوظة")
        version_label.setStyleSheet("color: #999; font-size: 12px; margin-top: 10px;")
        version_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.addWidget(version_label)

        # إضافة دعم السحب للنافذة
        self.oldPos = None

    def mousePressEvent(self, event):
        """تنفيذ عند الضغط على الماوس"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.oldPos = event.globalPosition().toPoint()

    def mouseMoveEvent(self, event):
        """تنفيذ عند تحريك الماوس"""
        if self.oldPos and event.buttons() == Qt.MouseButton.LeftButton:
            delta = QPoint(event.globalPosition().toPoint() - self.oldPos)
            self.move(self.x() + delta.x(), self.y() + delta.y())
            self.oldPos = event.globalPosition().toPoint()

    def mouseReleaseEvent(self, event):
        """تنفيذ عند ترك زر الماوس"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.oldPos = None

    def login(self):
        """التحقق من بيانات تسجيل الدخول"""
        username = self.username_input.text()
        password = self.password_input.text()

        if not username or not password:
            self.show_error_message("يرجى إدخال اسم المستخدم وكلمة المرور")
            return

        # التحقق من صحة بيانات المستخدم
        user = self.db.authenticate_user(username, password)

        if user:
            if self.on_login_success:
                self.on_login_success(user)
            self.accept()  # استخدام accept بدلاً من close
        else:
            self.show_error_message("اسم المستخدم أو كلمة المرور غير صحيحة")

    def closeEvent(self, event):
        """معالج إغلاق النافذة"""
        # السماح بإغلاق النافذة دائماً
        event.accept()

    def accept(self):
        """قبول تسجيل الدخول"""
        self._login_successful = True
        super().accept()

    def close_application(self):
        """إغلاق التطبيق بالكامل"""
        import sys
        sys.exit()

    def show_error_message(self, message):
        """عرض رسالة خطأ بتصميم جميل"""
        error_dialog = QMessageBox(self)
        error_dialog.setWindowTitle("خطأ")
        error_dialog.setText(message)
        error_dialog.setIcon(QMessageBox.Icon.Warning)
        error_dialog.setStandardButtons(QMessageBox.StandardButton.Ok)
        error_dialog.setStyleSheet("""
            QMessageBox {
                background-color: #ffffff;
                font-family: 'Arial';
            }
            QMessageBox QLabel {
                color: #333333;
                font-size: 14px;
            }
            QPushButton {
                background-color: #1a3a63;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 14px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #2c5282;
            }
        """)
        error_dialog.exec()


def main():
    """الدالة الرئيسية لتشغيل نافذة تسجيل الدخول"""
    app = QApplication(sys.argv)

    # تعيين اتجاه التطبيق من اليمين إلى اليسار للغة العربية
    app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

    # تعيين الخط للتطبيق
    font = QFont("Arial", 10)
    app.setFont(font)

    def on_login_success(user):
        print(f"تم تسجيل الدخول بنجاح: {user['username']}")

    window = LoginWindow(on_login_success=on_login_success)
    window.show()

    sys.exit(app.exec())


if __name__ == "__main__":
    main()
