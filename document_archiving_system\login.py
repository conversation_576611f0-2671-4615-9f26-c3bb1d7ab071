#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
واجهة تسجيل الدخول
تتعامل مع عملية تسجيل الدخول والتحقق من المستخدم
"""

import sys
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout, 
                            QPushButton, QLabel, QLineEdit, QMessageBox)
from PyQt5.QtGui import QIcon, QFont, QPixmap
from PyQt5.QtCore import Qt, QSize

from database import Database


class LoginWindow(QWidget):
    """نافذة تسجيل الدخول"""
    
    def __init__(self, on_login_success=None):
        super().__init__()
        
        # إعداد النافذة
        self.setWindowTitle("تسجيل الدخول - نظام الأرشفة الإلكترونية")
        self.setFixedSize(400, 300)
        self.setStyleSheet("background-color: #f5f5f5;")
        
        # تهيئة قاعدة البيانات
        self.db = Database()
        
        # دالة رد الاتصال عند نجاح تسجيل الدخول
        self.on_login_success = on_login_success
        
        # إنشاء واجهة المستخدم
        self.init_ui()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # عنوان التطبيق
        title_label = QLabel("نظام الأرشفة الإلكترونية")
        title_label.setStyleSheet("""
            font-size: 20px;
            font-weight: bold;
            color: #1a3a63;
        """)
        title_label.setAlignment(Qt.AlignCenter)
        
        # عنوان تسجيل الدخول
        login_label = QLabel("تسجيل الدخول")
        login_label.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
        """)
        login_label.setAlignment(Qt.AlignCenter)
        
        # حقل اسم المستخدم
        username_layout = QVBoxLayout()
        username_label = QLabel("اسم المستخدم:")
        username_label.setStyleSheet("font-size: 14px;")
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        self.username_input.setStyleSheet("""
            QLineEdit {
                padding: 10px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
            }
        """)
        username_layout.addWidget(username_label)
        username_layout.addWidget(self.username_input)
        
        # حقل كلمة المرور
        password_layout = QVBoxLayout()
        password_label = QLabel("كلمة المرور:")
        password_label.setStyleSheet("font-size: 14px;")
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setStyleSheet("""
            QLineEdit {
                padding: 10px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
            }
        """)
        password_layout.addWidget(password_label)
        password_layout.addWidget(self.password_input)
        
        # زر تسجيل الدخول
        login_button = QPushButton("تسجيل الدخول")
        login_button.setStyleSheet("""
            QPushButton {
                background-color: #1e81b0;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 4px;
                font-size: 16px;
            }
            QPushButton:hover {
                background-color: #166d96;
            }
        """)
        login_button.clicked.connect(self.login)
        
        # إضافة العناصر إلى التخطيط الرئيسي
        main_layout.addWidget(title_label)
        main_layout.addWidget(login_label)
        main_layout.addLayout(username_layout)
        main_layout.addLayout(password_layout)
        main_layout.addWidget(login_button)
        
        # معلومات الإصدار
        version_label = QLabel("الإصدار 1.0")
        version_label.setStyleSheet("color: #666; font-size: 12px;")
        version_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(version_label)
    
    def login(self):
        """التحقق من بيانات تسجيل الدخول"""
        username = self.username_input.text()
        password = self.password_input.text()
        
        if not username or not password:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        # التحقق من صحة بيانات المستخدم
        user = self.db.authenticate_user(username, password)
        
        if user:
            if self.on_login_success:
                self.on_login_success(user)
            self.close()
        else:
            QMessageBox.warning(self, "خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")


def main():
    """الدالة الرئيسية لتشغيل نافذة تسجيل الدخول"""
    app = QApplication(sys.argv)
    
    # تعيين اتجاه التطبيق من اليمين إلى اليسار للغة العربية
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تعيين الخط للتطبيق
    font = QFont("Arial", 10)
    app.setFont(font)
    
    def on_login_success(user):
        print(f"تم تسجيل الدخول بنجاح: {user['username']}")
    
    window = LoginWindow(on_login_success=on_login_success)
    window.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
