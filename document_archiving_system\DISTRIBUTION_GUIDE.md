# دليل التوزيع - نظام أرشفة الكتب الإلكترونية

## 📦 ملفات التوزيع

### الملفات الأساسية
- `main.py` - الملف الرئيسي للبرنامج
- `database.py` - إدارة قاعدة البيانات
- `login.py` - نظام تسجيل الدخول
- `books_view.py` - واجهة إدارة الكتب
- `book_manager.py` - منطق إدارة الكتب
- `users.py` - إدارة المستخدمين
- `settings.py` - إعدادات النظام
- `departments.py` - إدارة الأقسام
- `updater.py` - نظام التحديث

### ملفات التكوين
- `requirements.txt` - المتطلبات البرمجية
- `version.json` - معلومات الإصدار
- `README.md` - دليل المستخدم

### ملفات التثبيت والتشغيل
- `install.bat` - سكريبت التثبيت لـ Windows
- `run.bat` - سكريبت التشغيل لـ Windows
- `build.py` - سكريبت البناء
- `build_config.py` - إعدادات البناء

## 🚀 طرق التوزيع

### 1. التوزيع كمصدر (Source Distribution)
```
BookArchiveSystem_v1.0.0_Source.zip
├── main.py
├── database.py
├── login.py
├── books_view.py
├── book_manager.py
├── users.py
├── settings.py
├── departments.py
├── updater.py
├── requirements.txt
├── version.json
├── README.md
├── install.bat
├── run.bat
└── DISTRIBUTION_GUIDE.md
```

**المتطلبات:**
- Python 3.7+
- pip

**التثبيت:**
1. فك ضغط الملف
2. تشغيل `install.bat` أو `pip install -r requirements.txt`
3. تشغيل `run.bat` أو `python main.py`

### 2. التوزيع كملف تنفيذي (Executable Distribution)
```
BookArchiveSystem_v1.0.0_Executable.zip
├── BookArchiveSystem.exe
├── README.md
├── version.json
└── assets/
```

**المتطلبات:**
- Windows 7/8/10/11
- لا يتطلب Python

**التثبيت:**
1. فك ضغط الملف
2. تشغيل `BookArchiveSystem.exe`

### 3. التوزيع المختلط (Hybrid Distribution)
```
BookArchiveSystem_v1.0.0_Complete.zip
├── Source/
│   ├── main.py
│   ├── [جميع ملفات المصدر]
│   └── requirements.txt
├── Executable/
│   ├── BookArchiveSystem.exe
│   └── assets/
├── README.md
├── INSTALLATION_GUIDE.md
└── version.json
```

## 🔧 إعداد التوزيع

### إنشاء حزمة المصدر
```bash
# تشغيل سكريبت البناء
python build_config.py

# سيتم إنشاء:
# - build/ (ملفات البناء)
# - dist/ (الملفات النهائية)
# - release/ (حزمة التوزيع)
```

### إنشاء ملف تنفيذي
```bash
# تثبيت PyInstaller
pip install pyinstaller

# إنشاء ملف تنفيذي
python build.py

# أو يدوياً
pyinstaller --onefile --windowed --name=BookArchiveSystem main.py
```

## 📋 قائمة التحقق قبل التوزيع

### ✅ الاختبارات الأساسية
- [ ] تشغيل البرنامج بنجاح
- [ ] تسجيل الدخول يعمل
- [ ] إضافة كتاب جديد
- [ ] حفظ واسترجاع البيانات
- [ ] وظيفة السكانر
- [ ] نظام التحديث

### ✅ الملفات المطلوبة
- [ ] جميع ملفات Python
- [ ] requirements.txt
- [ ] README.md
- [ ] version.json
- [ ] ملفات التثبيت (.bat)

### ✅ التوافق
- [ ] Windows 7/8/10/11
- [ ] Python 3.7+
- [ ] PyQt5 5.15+

### ✅ الأمان
- [ ] لا توجد كلمات مرور مكشوفة
- [ ] ملفات قاعدة البيانات آمنة
- [ ] صلاحيات الملفات صحيحة

## 🔄 نظام التحديث

### إعداد خادم التحديثات
1. رفع الإصدار الجديد إلى GitHub Releases
2. تحديث رابط التحديث في `version.json`
3. إنشاء ملف changelog

### تحديث معلومات الإصدار
```json
{
  "version": "1.1.0",
  "build_date": "2024-02-01T10:00:00",
  "update_url": "https://github.com/your-repo/releases/latest",
  "changelog": [
    "إضافة ميزات جديدة",
    "إصلاح الأخطاء",
    "تحسين الأداء"
  ]
}
```

## 📊 إحصائيات التوزيع

### أحجام الملفات المتوقعة
- **المصدر:** ~2-5 MB
- **الملف التنفيذي:** ~50-100 MB
- **الحزمة الكاملة:** ~60-120 MB

### متطلبات التحميل
- **سرعة الإنترنت:** 1 Mbps أو أعلى
- **وقت التحميل:** 1-10 دقائق حسب السرعة

## 🎯 استراتيجية التوزيع

### المرحلة الأولى: التوزيع المحدود
- إصدار نسخة تجريبية
- اختبار مع مجموعة صغيرة
- جمع التعليقات والملاحظات

### المرحلة الثانية: التوزيع العام
- إصدار النسخة النهائية
- توفير دعم فني
- تحديثات دورية

### المرحلة الثالثة: التطوير المستمر
- إضافة ميزات جديدة
- تحسين الأداء
- دعم منصات إضافية

## 📞 الدعم الفني للتوزيع

### للمطورين
- دليل البناء والتطوير
- وثائق API
- أمثلة التخصيص

### للمستخدمين النهائيين
- دليل التثبيت المفصل
- فيديوهات تعليمية
- الأسئلة الشائعة

---

**تم إعداد هذا الدليل لضمان توزيع ناجح وسلس للنظام**
