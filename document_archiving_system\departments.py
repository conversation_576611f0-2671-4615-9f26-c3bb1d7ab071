#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
إدارة الأقسام
تتعامل مع عرض وإضافة وتعديل الأقسام
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, 
                            QLineEdit, QTableWidget, QTableWidgetItem, QHeaderView,
                            QMessageBox, QDialog, QFormLayout)
from PyQt5.QtGui import QIcon, QFont
from PyQt5.QtCore import Qt


class DepartmentsWidget(QWidget):
    """واجهة إدارة الأقسام"""
    
    def __init__(self, db):
        super().__init__()
        
        # تهيئة قاعدة البيانات
        self.db = db
        
        # إنشاء واجهة المستخدم
        self.init_ui()
        
        # تحميل الأقسام
        self.load_departments()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # عنوان الصفحة
        title_label = QLabel("إدارة الأقسام")
        title_label.setStyleSheet("""
            font-size: 20px;
            font-weight: bold;
            color: #1a3a63;
        """)
        
        # شريط الأدوات
        toolbar_layout = QHBoxLayout()
        
        # زر إضافة قسم جديد
        add_button = QPushButton("إضافة قسم جديد")
        add_button.setStyleSheet("""
            QPushButton {
                background-color: #16a085;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #138a72;
            }
        """)
        add_button.clicked.connect(self.add_department)
        
        # حقل البحث
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("بحث في الأقسام...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
            }
        """)
        self.search_input.textChanged.connect(self.filter_departments)
        
        toolbar_layout.addWidget(add_button)
        toolbar_layout.addStretch(1)
        toolbar_layout.addWidget(self.search_input)
        
        # جدول الأقسام
        self.departments_table = QTableWidget()
        self.departments_table.setColumnCount(4)
        self.departments_table.setHorizontalHeaderLabels(["المعرف", "اسم القسم", "الوصف", "الإجراءات"])
        self.departments_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.departments_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Stretch)
        self.departments_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }
            QHeaderView::section {
                background-color: #1a3a63;
                color: white;
                padding: 8px;
                font-size: 14px;
                font-weight: bold;
                border: none;
            }
        """)
        
        # إضافة العناصر إلى التخطيط الرئيسي
        main_layout.addWidget(title_label)
        main_layout.addLayout(toolbar_layout)
        main_layout.addWidget(self.departments_table)
    
    def load_departments(self):
        """تحميل الأقسام من قاعدة البيانات"""
        departments = self.db.get_departments()
        
        # تفريغ الجدول
        self.departments_table.setRowCount(0)
        
        # إضافة الأقسام إلى الجدول
        for row, department in enumerate(departments):
            self.departments_table.insertRow(row)
            
            # إضافة بيانات القسم
            id_item = QTableWidgetItem(str(department['id']))
            name_item = QTableWidgetItem(department['name'])
            description_item = QTableWidgetItem(department['description'] or "")
            
            self.departments_table.setItem(row, 0, id_item)
            self.departments_table.setItem(row, 1, name_item)
            self.departments_table.setItem(row, 2, description_item)
            
            # إضافة أزرار الإجراءات
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(0, 0, 0, 0)
            
            edit_button = QPushButton("تعديل")
            edit_button.setStyleSheet("""
                QPushButton {
                    background-color: #1e81b0;
                    color: white;
                    border: none;
                    padding: 5px 10px;
                    border-radius: 3px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #166d96;
                }
            """)
            
            delete_button = QPushButton("حذف")
            delete_button.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border: none;
                    padding: 5px 10px;
                    border-radius: 3px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
            """)
            
            # ربط الأزرار بالإجراءات
            department_id = department['id']
            edit_button.clicked.connect(lambda checked, dept_id=department_id: self.edit_department(dept_id))
            delete_button.clicked.connect(lambda checked, dept_id=department_id: self.delete_department(dept_id))
            
            actions_layout.addWidget(edit_button)
            actions_layout.addWidget(delete_button)
            
            self.departments_table.setCellWidget(row, 3, actions_widget)
    
    def filter_departments(self):
        """تصفية الأقسام حسب البحث"""
        search_text = self.search_input.text().lower()
        
        for row in range(self.departments_table.rowCount()):
            name_item = self.departments_table.item(row, 1)
            description_item = self.departments_table.item(row, 2)
            
            if (name_item and search_text in name_item.text().lower()) or \
               (description_item and search_text in description_item.text().lower()):
                self.departments_table.setRowHidden(row, False)
            else:
                self.departments_table.setRowHidden(row, True)
    
    def add_department(self):
        """إضافة قسم جديد"""
        dialog = DepartmentDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            name = dialog.name_input.text()
            description = dialog.description_input.text()
            
            if name:
                department_id = self.db.add_department(name, description)
                if department_id:
                    self.load_departments()
                    QMessageBox.information(self, "نجاح", f"تم إضافة القسم '{name}' بنجاح")
                else:
                    QMessageBox.warning(self, "خطأ", "فشل في إضافة القسم")
    
    def edit_department(self, department_id):
        """تعديل قسم"""
        # استرجاع بيانات القسم
        self.db.cursor.execute("SELECT * FROM departments WHERE id = ?", (department_id,))
        department = self.db.cursor.fetchone()
        
        if not department:
            QMessageBox.warning(self, "خطأ", "القسم غير موجود")
            return
        
        dialog = DepartmentDialog(self, department=dict(department))
        if dialog.exec_() == QDialog.Accepted:
            name = dialog.name_input.text()
            description = dialog.description_input.text()
            
            if name:
                try:
                    self.db.cursor.execute(
                        "UPDATE departments SET name = ?, description = ? WHERE id = ?",
                        (name, description, department_id)
                    )
                    self.db.conn.commit()
                    self.load_departments()
                    QMessageBox.information(self, "نجاح", f"تم تعديل القسم '{name}' بنجاح")
                except Exception as e:
                    QMessageBox.warning(self, "خطأ", f"فشل في تعديل القسم: {str(e)}")
    
    def delete_department(self, department_id):
        """حذف قسم"""
        # التحقق من وجود مستندات مرتبطة بالقسم
        self.db.cursor.execute("SELECT COUNT(*) FROM documents WHERE department_id = ?", (department_id,))
        documents_count = self.db.cursor.fetchone()[0]
        
        if documents_count > 0:
            QMessageBox.warning(
                self, 
                "تحذير", 
                f"لا يمكن حذف هذا القسم لأنه يحتوي على {documents_count} مستند. قم بنقل المستندات إلى قسم آخر أولاً."
            )
            return
        
        # تأكيد الحذف
        reply = QMessageBox.question(
            self, 
            "تأكيد الحذف", 
            "هل أنت متأكد من حذف هذا القسم؟",
            QMessageBox.Yes | QMessageBox.No, 
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                self.db.cursor.execute("DELETE FROM departments WHERE id = ?", (department_id,))
                self.db.conn.commit()
                self.load_departments()
                QMessageBox.information(self, "نجاح", "تم حذف القسم بنجاح")
            except Exception as e:
                QMessageBox.warning(self, "خطأ", f"فشل في حذف القسم: {str(e)}")


class DepartmentDialog(QDialog):
    """نافذة حوار إضافة/تعديل قسم"""
    
    def __init__(self, parent=None, department=None):
        super().__init__(parent)
        
        # تعيين عنوان النافذة
        self.setWindowTitle("إضافة قسم جديد" if not department else "تعديل قسم")
        self.setFixedSize(400, 200)
        
        # إنشاء واجهة المستخدم
        self.init_ui(department)
    
    def init_ui(self, department=None):
        """تهيئة واجهة المستخدم"""
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        
        # نموذج البيانات
        form_layout = QFormLayout()
        
        # حقل اسم القسم
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("أدخل اسم القسم")
        self.name_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
            }
        """)
        
        # حقل وصف القسم
        self.description_input = QLineEdit()
        self.description_input.setPlaceholderText("أدخل وصف القسم (اختياري)")
        self.description_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
            }
        """)
        
        # إضافة الحقول إلى النموذج
        form_layout.addRow("اسم القسم:", self.name_input)
        form_layout.addRow("الوصف:", self.description_input)
        
        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        
        save_button = QPushButton("حفظ")
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #16a085;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #138a72;
            }
        """)
        save_button.clicked.connect(self.accept)
        
        cancel_button = QPushButton("إلغاء")
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        cancel_button.clicked.connect(self.reject)
        
        buttons_layout.addWidget(save_button)
        buttons_layout.addWidget(cancel_button)
        
        # إضافة العناصر إلى التخطيط الرئيسي
        main_layout.addLayout(form_layout)
        main_layout.addStretch(1)
        main_layout.addLayout(buttons_layout)
        
        # تعبئة البيانات إذا كان هناك قسم للتعديل
        if department:
            self.name_input.setText(department['name'])
            self.description_input.setText(department['description'] or "")
