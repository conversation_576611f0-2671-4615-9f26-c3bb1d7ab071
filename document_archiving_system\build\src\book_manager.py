#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
مدير الكتب
يتعامل مع عمليات إدارة الكتب والصور
"""

import os
import shutil
import json
from datetime import datetime


class BookManager:
    """مدير الكتب"""
    
    def __init__(self, db):
        self.db = db
        
        # إنشاء مجلد الكتب إذا لم يكن موجودًا
        self.books_folder = "books"
        if not os.path.exists(self.books_folder):
            os.makedirs(self.books_folder)
        
        # إنشاء مجلد الصور إذا لم يكن موجودًا
        self.images_folder = os.path.join(self.books_folder, "images")
        if not os.path.exists(self.images_folder):
            os.makedirs(self.images_folder)
    
    def get_book_types(self):
        """الحصول على أنواع الكتب"""
        try:
            book_types = self.db.get_book_types()
            return {'success': True, 'book_types': book_types}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def add_book(self, book_data, images=None):
        """إضافة كتاب جديد"""
        try:
            # إضافة الكتاب إلى قاعدة البيانات
            book_id = self.db.add_book(
                book_number=book_data['book_number'],
                book_type_id=book_data['book_type_id'],
                date=book_data['date'],
                notes=book_data.get('notes', ''),
                piece_number=book_data.get('piece_number', ''),
                district_name=book_data.get('district_name', ''),
                department_id=book_data.get('department_id'),
                created_by=book_data.get('created_by')
            )
            
            if not book_id:
                return {'success': False, 'error': 'فشل في إضافة الكتاب إلى قاعدة البيانات'}
            
            # إضافة الصور إذا كانت موجودة
            if images:
                for image_path in images:
                    self.add_book_image(book_id, image_path)
            
            return {'success': True, 'book_id': book_id}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def add_book_image(self, book_id, image_path):
        """إضافة صورة للكتاب"""
        try:
            if not os.path.exists(image_path):
                return {'success': False, 'error': 'الملف غير موجود'}
            
            # إنشاء مجلد للكتاب
            book_folder = os.path.join(self.images_folder, str(book_id))
            if not os.path.exists(book_folder):
                os.makedirs(book_folder)
            
            # نسخ الصورة إلى مجلد الكتاب
            image_name = os.path.basename(image_path)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            new_image_name = f"{timestamp}_{image_name}"
            new_image_path = os.path.join(book_folder, new_image_name)
            
            shutil.copy2(image_path, new_image_path)
            
            # الحصول على حجم الصورة
            image_size = os.path.getsize(new_image_path)
            
            # إضافة معلومات الصورة إلى قاعدة البيانات
            image_id = self.db.add_book_image(book_id, new_image_path, new_image_name, image_size)
            
            if image_id:
                return {'success': True, 'image_id': image_id, 'image_path': new_image_path}
            else:
                # حذف الصورة إذا فشل في إضافتها إلى قاعدة البيانات
                if os.path.exists(new_image_path):
                    os.remove(new_image_path)
                return {'success': False, 'error': 'فشل في إضافة الصورة إلى قاعدة البيانات'}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def get_books(self, department_id=None, book_type_id=None):
        """الحصول على الكتب"""
        try:
            books = self.db.get_books(department_id, book_type_id)
            return {'success': True, 'books': books}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def search_books(self, search_term):
        """البحث في الكتب"""
        try:
            books = self.db.search_books(search_term)
            return {'success': True, 'books': books}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def get_book_images(self, book_id):
        """الحصول على صور الكتاب"""
        try:
            images = self.db.get_book_images(book_id)
            return {'success': True, 'images': images}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def delete_book_image(self, image_id):
        """حذف صورة الكتاب"""
        try:
            # الحصول على معلومات الصورة
            self.db.cursor.execute("SELECT * FROM book_images WHERE id = ?", (image_id,))
            image = self.db.cursor.fetchone()
            
            if not image:
                return {'success': False, 'error': 'الصورة غير موجودة'}
            
            image_dict = dict(image)
            
            # حذف الصورة من قاعدة البيانات
            self.db.cursor.execute("DELETE FROM book_images WHERE id = ?", (image_id,))
            self.db.conn.commit()
            
            # حذف الصورة من النظام
            if os.path.exists(image_dict['image_path']):
                os.remove(image_dict['image_path'])
            
            return {'success': True}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def scan_images_from_scanner(self, book_id):
        """مسح الصور من السكانر"""
        try:
            # هذه الدالة تحتاج إلى مكتبة خاصة للتعامل مع السكانر
            # يمكن استخدام مكتبة مثل python-sane أو twain
            # هنا سنضع كود أساسي للمحاكاة
            
            # محاكاة مسح الصور
            scanned_images = []
            
            # في التطبيق الحقيقي، ستكون هناك واجهة للتعامل مع السكانر
            # وسيتم حفظ الصور الممسوحة في مجلد مؤقت
            
            return {'success': True, 'images': scanned_images, 'message': 'تم مسح الصور بنجاح'}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def get_book_by_id(self, book_id):
        """الحصول على كتاب بالمعرف"""
        try:
            self.db.cursor.execute("""
                SELECT b.*, bt.name as book_type_name, d.name as department_name, u.full_name as created_by_name
                FROM books b
                LEFT JOIN book_types bt ON b.book_type_id = bt.id
                LEFT JOIN departments d ON b.department_id = d.id
                LEFT JOIN users u ON b.created_by = u.id
                WHERE b.id = ?
            """, (book_id,))
            
            book = self.db.cursor.fetchone()
            if book:
                return {'success': True, 'book': dict(book)}
            else:
                return {'success': False, 'error': 'الكتاب غير موجود'}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def update_book(self, book_id, book_data):
        """تحديث بيانات الكتاب"""
        try:
            self.db.cursor.execute("""
                UPDATE books SET 
                book_number = ?, date = ?, notes = ?, piece_number = ?, district_name = ?, department_id = ?
                WHERE id = ?
            """, (
                book_data['book_number'],
                book_data['date'],
                book_data.get('notes', ''),
                book_data.get('piece_number', ''),
                book_data.get('district_name', ''),
                book_data.get('department_id'),
                book_id
            ))
            
            self.db.conn.commit()
            return {'success': True}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def delete_book(self, book_id):
        """حذف الكتاب"""
        try:
            # حذف صور الكتاب أولاً
            images = self.db.get_book_images(book_id)
            for image in images:
                if os.path.exists(image['image_path']):
                    os.remove(image['image_path'])
            
            # حذف مجلد الكتاب
            book_folder = os.path.join(self.images_folder, str(book_id))
            if os.path.exists(book_folder):
                shutil.rmtree(book_folder)
            
            # حذف الكتاب من قاعدة البيانات
            self.db.cursor.execute("DELETE FROM books WHERE id = ?", (book_id,))
            self.db.conn.commit()
            
            return {'success': True}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def export_book_data(self, book_id):
        """تصدير بيانات الكتاب"""
        try:
            # الحصول على بيانات الكتاب
            book_result = self.get_book_by_id(book_id)
            if not book_result['success']:
                return book_result
            
            book = book_result['book']
            
            # الحصول على صور الكتاب
            images_result = self.get_book_images(book_id)
            if images_result['success']:
                book['images'] = images_result['images']
            
            return {'success': True, 'book_data': book}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
