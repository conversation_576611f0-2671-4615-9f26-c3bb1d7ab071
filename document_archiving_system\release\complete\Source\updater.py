#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
نظام التحديث للبرنامج
"""

import os
import sys
import json
import requests
import zipfile
import shutil
from datetime import datetime
from PyQt5.QtWidgets import QDialog, QVBoxLayout, QLabel, QPushButton, QProgressBar, QTextEdit, QMessageBox
from PyQt5.QtCore import QThread, pyqtSignal, Qt
from PyQt5.QtGui import QFont

# معلومات الإصدار الحالي
CURRENT_VERSION = "1.0.0"
UPDATE_SERVER_URL = "https://api.github.com/repos/your-repo/releases/latest"  # يجب تغييرها
BACKUP_FOLDER = "backup"

class UpdateChecker(QThread):
    """فئة فحص التحديثات"""
    
    update_available = pyqtSignal(dict)
    no_update = pyqtSignal()
    error_occurred = pyqtSignal(str)
    
    def run(self):
        """فحص التحديثات"""
        try:
            # محاكاة فحص التحديثات (يمكن تخصيصها لاحقاً)
            # في التطبيق الحقيقي، ستتصل بخادم التحديثات
            
            # مثال على بيانات التحديث
            update_info = {
                "version": "1.1.0",
                "release_date": "2024-01-15",
                "download_url": "https://example.com/update.zip",
                "changelog": [
                    "إضافة ميزات جديدة لإدارة الصور",
                    "تحسين واجهة المستخدم",
                    "إصلاح الأخطاء المعروفة",
                    "تحسين الأداء"
                ],
                "size": "15.2 MB",
                "required": False
            }
            
            # مقارنة الإصدارات
            if self.compare_versions(update_info["version"], CURRENT_VERSION) > 0:
                self.update_available.emit(update_info)
            else:
                self.no_update.emit()
                
        except Exception as e:
            self.error_occurred.emit(str(e))
    
    def compare_versions(self, version1, version2):
        """مقارنة إصدارين"""
        v1_parts = [int(x) for x in version1.split('.')]
        v2_parts = [int(x) for x in version2.split('.')]
        
        # إضافة أصفار إذا لزم الأمر
        while len(v1_parts) < len(v2_parts):
            v1_parts.append(0)
        while len(v2_parts) < len(v1_parts):
            v2_parts.append(0)
        
        for i in range(len(v1_parts)):
            if v1_parts[i] > v2_parts[i]:
                return 1
            elif v1_parts[i] < v2_parts[i]:
                return -1
        
        return 0

class UpdateDownloader(QThread):
    """فئة تحميل التحديثات"""
    
    progress_updated = pyqtSignal(int)
    download_completed = pyqtSignal(str)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, download_url, file_path):
        super().__init__()
        self.download_url = download_url
        self.file_path = file_path
    
    def run(self):
        """تحميل التحديث"""
        try:
            # محاكاة التحميل
            import time
            for i in range(101):
                time.sleep(0.05)  # محاكاة التحميل
                self.progress_updated.emit(i)
            
            # في التطبيق الحقيقي، ستحمل الملف من الخادم
            # response = requests.get(self.download_url, stream=True)
            # total_size = int(response.headers.get('content-length', 0))
            
            self.download_completed.emit(self.file_path)
            
        except Exception as e:
            self.error_occurred.emit(str(e))

class UpdateDialog(QDialog):
    """نافذة التحديث"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("فحص التحديثات - نظام أرشفة الكتب")
        self.setFixedSize(500, 400)
        self.setModal(True)
        
        self.update_checker = UpdateChecker()
        self.update_downloader = None
        
        self.init_ui()
        self.connect_signals()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # العنوان
        title = QLabel("فحص التحديثات")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #1a3a63;
            padding: 10px;
        """)
        
        # معلومات الإصدار الحالي
        current_version_label = QLabel(f"الإصدار الحالي: {CURRENT_VERSION}")
        current_version_label.setStyleSheet("""
            font-size: 14px;
            color: #666;
            padding: 5px;
        """)
        
        # منطقة المعلومات
        self.info_text = QTextEdit()
        self.info_text.setReadOnly(True)
        self.info_text.setMaximumHeight(150)
        self.info_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 10px;
                background-color: #f9f9f9;
            }
        """)
        self.info_text.setText("جاري فحص التحديثات...")
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #ddd;
                border-radius: 5px;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #1a3a63;
                border-radius: 5px;
            }
        """)
        
        # الأزرار
        buttons_layout = QVBoxLayout()
        
        self.check_button = QPushButton("فحص التحديثات")
        self.check_button.setStyleSheet("""
            QPushButton {
                background-color: #1a3a63;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2c5282;
            }
        """)
        
        self.download_button = QPushButton("تحميل التحديث")
        self.download_button.setVisible(False)
        self.download_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        self.close_button = QPushButton("إغلاق")
        self.close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        buttons_layout.addWidget(self.check_button)
        buttons_layout.addWidget(self.download_button)
        buttons_layout.addWidget(self.close_button)
        
        # إضافة العناصر
        layout.addWidget(title)
        layout.addWidget(current_version_label)
        layout.addWidget(self.info_text)
        layout.addWidget(self.progress_bar)
        layout.addLayout(buttons_layout)
    
    def connect_signals(self):
        """ربط الإشارات"""
        self.check_button.clicked.connect(self.check_for_updates)
        self.download_button.clicked.connect(self.download_update)
        self.close_button.clicked.connect(self.close)
        
        self.update_checker.update_available.connect(self.on_update_available)
        self.update_checker.no_update.connect(self.on_no_update)
        self.update_checker.error_occurred.connect(self.on_error)
    
    def check_for_updates(self):
        """فحص التحديثات"""
        self.check_button.setEnabled(False)
        self.info_text.setText("جاري فحص التحديثات...")
        self.update_checker.start()
    
    def on_update_available(self, update_info):
        """عند توفر تحديث"""
        self.check_button.setEnabled(True)
        
        changelog_text = "\n".join([f"• {item}" for item in update_info["changelog"]])
        
        info_text = f"""
تحديث جديد متوفر!

الإصدار الجديد: {update_info["version"]}
تاريخ الإصدار: {update_info["release_date"]}
حجم التحديث: {update_info["size"]}

التحسينات الجديدة:
{changelog_text}
        """
        
        self.info_text.setText(info_text.strip())
        self.download_button.setVisible(True)
        self.update_info = update_info
    
    def on_no_update(self):
        """عند عدم توفر تحديث"""
        self.check_button.setEnabled(True)
        self.info_text.setText("لا توجد تحديثات متوفرة.\nأنت تستخدم أحدث إصدار من البرنامج.")
    
    def on_error(self, error_message):
        """عند حدوث خطأ"""
        self.check_button.setEnabled(True)
        self.info_text.setText(f"حدث خطأ أثناء فحص التحديثات:\n{error_message}")
    
    def download_update(self):
        """تحميل التحديث"""
        if not hasattr(self, 'update_info'):
            return
        
        self.download_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.info_text.setText("جاري تحميل التحديث...")
        
        # إنشاء مجلد التحديثات
        updates_folder = "updates"
        if not os.path.exists(updates_folder):
            os.makedirs(updates_folder)
        
        file_path = os.path.join(updates_folder, "update.zip")
        
        self.update_downloader = UpdateDownloader(self.update_info["download_url"], file_path)
        self.update_downloader.progress_updated.connect(self.progress_bar.setValue)
        self.update_downloader.download_completed.connect(self.on_download_completed)
        self.update_downloader.error_occurred.connect(self.on_download_error)
        self.update_downloader.start()
    
    def on_download_completed(self, file_path):
        """عند اكتمال التحميل"""
        self.progress_bar.setVisible(False)
        self.download_button.setEnabled(True)
        
        QMessageBox.information(
            self,
            "اكتمل التحميل",
            "تم تحميل التحديث بنجاح!\n\nسيتم تطبيق التحديث عند إعادة تشغيل البرنامج."
        )
        
        self.info_text.setText("تم تحميل التحديث بنجاح!\nأعد تشغيل البرنامج لتطبيق التحديث.")
    
    def on_download_error(self, error_message):
        """عند حدوث خطأ في التحميل"""
        self.progress_bar.setVisible(False)
        self.download_button.setEnabled(True)
        self.info_text.setText(f"حدث خطأ أثناء تحميل التحديث:\n{error_message}")

def check_for_updates_on_startup():
    """فحص التحديثات عند بدء التشغيل"""
    # يمكن استدعاء هذه الدالة عند بدء البرنامج
    pass

def get_current_version():
    """الحصول على الإصدار الحالي"""
    return CURRENT_VERSION

def create_version_file():
    """إنشاء ملف معلومات الإصدار"""
    version_info = {
        "version": CURRENT_VERSION,
        "build_date": datetime.now().isoformat(),
        "features": [
            "نظام إدارة الكتب الإلكترونية",
            "إضافة وتعديل الكتب",
            "إدارة الصور والمرفقات",
            "وظيفة السكانر المتقدمة",
            "نظام التحديث التلقائي"
        ]
    }
    
    with open("version.json", "w", encoding="utf-8") as f:
        json.dump(version_info, f, ensure_ascii=False, indent=2)

if __name__ == "__main__":
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
    
    dialog = UpdateDialog()
    dialog.show()
    
    sys.exit(app.exec())
