# نظام أرشفة المستندات الورقية

برنامج سطح مكتب لإدارة وأرشفة المستندات الورقية مكتوب بلغة بايثون باستخدام مكتبة PyQt5 وقاعدة بيانات SQLite.

## المميزات

- واجهة مستخدم رسومية سهلة الاستخدام باللغة العربية
- إدارة الأقسام والمستندات
- البحث في المستندات
- تصنيف المستندات حسب الأقسام
- نظام تسجيل دخول للمستخدمين
- تخزين المستندات بشكل منظم
- إحصائيات عن المستندات والأقسام

## متطلبات التشغيل

- Python 3.6 أو أحدث
- المكتبات المطلوبة (موجودة في ملف requirements.txt):
  - PyQt5
  - python-dateutil

## التثبيت

1. قم بتثبيت بايثون من [الموقع الرسمي](https://www.python.org/downloads/)
2. قم بتنزيل أو استنساخ هذا المشروع
3. افتح موجه الأوامر (Command Prompt) في مجلد المشروع
4. قم بتثبيت المكتبات المطلوبة:

```
pip install -r requirements.txt
```

## تشغيل البرنامج

لتشغيل البرنامج، قم بتنفيذ الأمر التالي في مجلد المشروع:

```
python main.py
```

## استخدام البرنامج

1. عند تشغيل البرنامج، ستظهر نافذة تسجيل الدخول
2. استخدم بيانات الدخول الافتراضية:
   - اسم المستخدم: admin
   - كلمة المرور: admin123
3. بعد تسجيل الدخول، ستظهر الواجهة الرئيسية للبرنامج
4. يمكنك التنقل بين الصفحات المختلفة باستخدام القائمة الجانبية:
   - الرئيسية: الصفحة الرئيسية للبرنامج
   - الأقسام: إدارة الأقسام (إضافة، تعديل، حذف)
   - المستندات: إدارة المستندات (إضافة، عرض، حذف)
5. لإضافة مستند جديد:
   - انتقل إلى صفحة المستندات
   - انقر على زر "إضافة مستند جديد"
   - أدخل بيانات المستند واختر الملف
   - انقر على زر "حفظ"
6. للبحث عن مستند:
   - استخدم حقل البحث في صفحة المستندات
   - أدخل نص البحث واضغط على زر "بحث"
7. لتصفية المستندات حسب القسم:
   - استخدم قائمة الأقسام في صفحة المستندات
   - اختر القسم المطلوب

## هيكل المشروع

- `main.py`: الملف الرئيسي للبرنامج
- `database.py`: التعامل مع قاعدة البيانات SQLite
- `document_manager.py`: إدارة المستندات
- `login.py`: واجهة تسجيل الدخول
- `departments.py`: إدارة الأقسام
- `documents_view.py`: عرض وإدارة المستندات
- `requirements.txt`: قائمة المكتبات المطلوبة
- `database.db`: ملف قاعدة البيانات
- `documents/`: مجلد تخزين المستندات

## الترخيص

هذا البرنامج مفتوح المصدر ومتاح للاستخدام والتعديل بحرية.

## المطور

تم تطوير هذا البرنامج بواسطة [اسمك هنا]
