#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
واجهة تسجيل الدخول
تتعامل مع عملية تسجيل الدخول والتحقق من المستخدم
"""

import sys
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout,
                            QPushButton, QLabel, QLineEdit, QMessageBox, QFrame,
                            QGraphicsDropShadowEffect, QCheckBox, QDialog)
from PyQt5.QtGui import QIcon, QFont, QPixmap, QColor
from PyQt5.QtCore import Qt, QSize, QPoint

from database import Database


class LoginWindow(QDialog):
    """نافذة تسجيل الدخول"""

    def __init__(self, on_login_success=None):
        super().__init__()

        # إعداد النافذة
        self.setWindowTitle("تسجيل الدخول - نظام أرشفة الكتب الإلكترونية")
        self.setFixedSize(450, 550)
        self.setStyleSheet("""
            QDialog {
                background-color: #f0f0f0;
                font-family: 'Segoe UI', 'Arial', sans-serif;
            }
        """)

        # إبقاء إطار النافذة العادي
        # self.setWindowFlags(Qt.WindowType.FramelessWindowHint)
        # self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)

        # متغيرات السحب
        self.drag_position = None

        # توسيط النافذة
        self.center_window()

        # تهيئة قاعدة البيانات
        self.db = Database()

        # دالة رد الاتصال عند نجاح تسجيل الدخول
        self.on_login_success = on_login_success

        # إنشاء واجهة المستخدم
        self.init_ui()

    def center_window(self):
        """توسيط النافذة في الشاشة"""
        from PyQt5.QtWidgets import QApplication
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )

    def init_ui(self):
        """تهيئة واجهة المستخدم"""

        # التخطيط الرئيسي البسيط
        layout = QVBoxLayout(self)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(15)

        # العنوان
        title_label = QLabel("نظام أرشفة الكتب")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            font-size: 20px;
            font-weight: bold;
            color: #1a3a63;
            margin: 10px 0;
        """)

        # عنوان فرعي
        subtitle_label = QLabel("تسجيل الدخول")
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle_label.setStyleSheet("""
            font-size: 16px;
            color: #666;
            margin-bottom: 20px;
        """)

        # حقل اسم المستخدم
        username_label = QLabel("اسم المستخدم:")
        username_label.setStyleSheet("font-weight: bold; color: #333;")

        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        self.username_input.setText("admin")
        self.username_input.setStyleSheet("""
            QLineEdit {
                padding: 10px;
                border: 1px solid #ccc;
                border-radius: 5px;
                font-size: 14px;
            }
            QLineEdit:focus {
                border: 2px solid #1a3a63;
            }
        """)

        # حقل كلمة المرور
        password_label = QLabel("كلمة المرور:")
        password_label.setStyleSheet("font-weight: bold; color: #333;")

        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setText("admin123")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setStyleSheet("""
            QLineEdit {
                padding: 10px;
                border: 1px solid #ccc;
                border-radius: 5px;
                font-size: 14px;
            }
            QLineEdit:focus {
                border: 2px solid #1a3a63;
            }
        """)

        # تذكر بيانات الدخول
        remember_layout = QHBoxLayout()
        self.remember_checkbox = QCheckBox("تذكر بيانات الدخول")
        self.remember_checkbox.setStyleSheet("""
            QCheckBox {
                font-size: 13px;
                color: #555555;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #e0e0e0;
                border-radius: 4px;
            }
            QCheckBox::indicator:checked {
                background-color: #1a3a63;
                border: 2px solid #1a3a63;
            }
        """)
        remember_layout.addWidget(self.remember_checkbox)
        remember_layout.addStretch()

        # زر تسجيل الدخول
        login_button = QPushButton("تسجيل الدخول")
        login_button.setStyleSheet("""
            QPushButton {
                background-color: #1a3a63;
                color: white;
                border: none;
                padding: 12px;
                border-radius: 5px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2c5282;
            }
        """)
        login_button.clicked.connect(self.login)



        # إضافة العناصر إلى التخطيط
        layout.addWidget(title_label)
        layout.addWidget(subtitle_label)
        layout.addWidget(username_label)
        layout.addWidget(self.username_input)
        layout.addWidget(password_label)
        layout.addWidget(self.password_input)
        layout.addLayout(remember_layout)
        layout.addWidget(login_button)

        # إضافة دعم السحب للنافذة
        self.oldPos = None

    def mousePressEvent(self, event):
        """تنفيذ عند الضغط على الماوس"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.oldPos = event.globalPosition().toPoint()

    def mouseMoveEvent(self, event):
        """تنفيذ عند تحريك الماوس"""
        if self.oldPos and event.buttons() == Qt.MouseButton.LeftButton:
            delta = QPoint(event.globalPosition().toPoint() - self.oldPos)
            self.move(self.x() + delta.x(), self.y() + delta.y())
            self.oldPos = event.globalPosition().toPoint()

    def mouseReleaseEvent(self, event):
        """تنفيذ عند ترك زر الماوس"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.oldPos = None

    def login(self):
        """التحقق من بيانات تسجيل الدخول"""
        username = self.username_input.text()
        password = self.password_input.text()

        if not username or not password:
            self.show_error_message("يرجى إدخال اسم المستخدم وكلمة المرور")
            return

        # التحقق من صحة بيانات المستخدم
        user = self.db.authenticate_user(username, password)

        if user:
            if self.on_login_success:
                self.on_login_success(user)
            self.accept()  # استخدام accept بدلاً من close
        else:
            self.show_error_message("اسم المستخدم أو كلمة المرور غير صحيحة")

    def closeEvent(self, event):
        """معالج إغلاق النافذة"""
        # السماح بإغلاق النافذة دائماً
        event.accept()

    def accept(self):
        """قبول تسجيل الدخول"""
        self._login_successful = True
        super().accept()

    def close_application(self):
        """إغلاق التطبيق بالكامل"""
        import sys
        sys.exit()

    def show_error_message(self, message):
        """عرض رسالة خطأ بتصميم جميل"""
        error_dialog = QMessageBox(self)
        error_dialog.setWindowTitle("خطأ")
        error_dialog.setText(message)
        error_dialog.setIcon(QMessageBox.Icon.Warning)
        error_dialog.setStandardButtons(QMessageBox.StandardButton.Ok)
        error_dialog.setStyleSheet("""
            QMessageBox {
                background-color: #ffffff;
                font-family: 'Arial';
            }
            QMessageBox QLabel {
                color: #333333;
                font-size: 14px;
            }
            QPushButton {
                background-color: #1a3a63;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 14px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #2c5282;
            }
        """)
        error_dialog.exec()


def main():
    """الدالة الرئيسية لتشغيل نافذة تسجيل الدخول"""
    app = QApplication(sys.argv)

    # تعيين اتجاه التطبيق من اليمين إلى اليسار للغة العربية
    app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

    # تعيين الخط للتطبيق
    font = QFont("Arial", 10)
    app.setFont(font)

    def on_login_success(user):
        print(f"تم تسجيل الدخول بنجاح: {user['username']}")

    window = LoginWindow(on_login_success=on_login_success)
    window.show()

    sys.exit(app.exec())


if __name__ == "__main__":
    main()
