#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
اختبار بسيط للتأكد من عمل PyQt5
"""

import sys
import traceback

try:
    from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget, QPushButton
    from PyQt5.QtCore import Qt
    from PyQt5.QtGui import QFont
    print("✅ تم استيراد PyQt5 بنجاح")
except ImportError as e:
    print(f"❌ خطأ في استيراد PyQt5: {e}")
    sys.exit(1)

class SimpleWindow(QMainWindow):
    """نافذة بسيطة للاختبار"""
    
    def __init__(self):
        super().__init__()
        print("🔧 إنشاء النافذة...")
        self.init_ui()
        print("✅ تم إنشاء النافذة بنجاح")
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("اختبار نظام أرشفة الكتب")
        self.setGeometry(100, 100, 400, 300)
        
        # إنشاء widget مركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # تخطيط
        layout = QVBoxLayout(central_widget)
        
        # تسمية
        label = QLabel("مرحباً! النظام يعمل بشكل صحيح 🎉")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #1a3a63;
                padding: 20px;
                background-color: #f0f8ff;
                border: 2px solid #1a3a63;
                border-radius: 10px;
                margin: 20px;
            }
        """)
        
        # زر
        button = QPushButton("اضغط هنا للاختبار")
        button.setStyleSheet("""
            QPushButton {
                font-size: 16px;
                font-weight: bold;
                color: white;
                background-color: #1a3a63;
                border: none;
                padding: 15px;
                border-radius: 8px;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #2c5282;
            }
        """)
        button.clicked.connect(self.button_clicked)
        
        # إضافة العناصر
        layout.addWidget(label)
        layout.addWidget(button)
    
    def button_clicked(self):
        """عند الضغط على الزر"""
        print("🎯 تم الضغط على الزر - النظام يعمل!")
        self.centralWidget().findChild(QLabel).setText("تم الضغط على الزر! النظام يعمل بشكل ممتاز ✨")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تشغيل البرنامج...")
    
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        print("✅ تم إنشاء QApplication")
        
        # تعيين الاتجاه والخط
        app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        font = QFont("Arial", 10)
        app.setFont(font)
        print("✅ تم تعيين الإعدادات")
        
        # إنشاء النافذة
        window = SimpleWindow()
        print("✅ تم إنشاء النافذة")
        
        # عرض النافذة
        window.show()
        print("✅ تم عرض النافذة")
        
        print("🎉 البرنامج جاهز! يجب أن تظهر النافذة الآن...")
        
        # تشغيل التطبيق
        result = app.exec()
        print(f"📋 انتهى التطبيق بالكود: {result}")
        
        return result
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل البرنامج: {e}")
        print("📋 تفاصيل الخطأ:")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    print("=" * 50)
    print("🔍 اختبار نظام أرشفة الكتب")
    print("=" * 50)
    
    exit_code = main()
    
    print("=" * 50)
    print(f"🏁 انتهى الاختبار بالكود: {exit_code}")
    print("=" * 50)
    
    sys.exit(exit_code)
